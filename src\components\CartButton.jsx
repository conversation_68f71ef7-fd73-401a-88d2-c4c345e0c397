import { useState } from 'react';
import { ShoppingBag } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCart } from '../context/CartContext';
import AnimatedNotification from './ui/AnimatedNotification';
import { useAuthCheck } from '../utils/authCheck';

export default function CartButton({
  product,
  selectedColor,
  selectedSize,
  quantity = 1,
  className = '',
  buttonText = 'Add to Cart',
  showIcon = true
}) {
  const { addToCart } = useCart();
  const [animate, setAnimate] = useState(false);
  const [success, setSuccess] = useState(false);
  const checkAuth = useAuthCheck();

  const handleClick = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Check if user is authenticated before adding to cart
    if (checkAuth('add to cart')) {
      setAnimate(true);
      
      // Prepare product with selected options
      const productToAdd = {
        ...product,
        color: selectedColor?.name || (typeof selectedColor === 'string' ? selectedColor : null) || product.colors?.[0]?.name || 'Default',
        colorHex: selectedColor?.hex || (typeof selectedColor === 'object' ? selectedColor.hex : null) || product.colors?.[0]?.hex || '#000000',
        size: selectedSize || (product.sizes?.[0]?.size_name || product.sizes?.[0]) || 'M'
      };
      
      // Add to cart
      const result = await addToCart(productToAdd, quantity);
      
      if (result) {
        setSuccess(true);
        
        // Reset animation state
        setTimeout(() => {
          setAnimate(false);
          setSuccess(false);
        }, 2000);
      } else {
        setAnimate(false);
      }
    }
  };

  return (
    <>
      <button
        className={`relative ${className}`}
        onClick={handleClick}
        aria-label="Add to cart"
      >
        <div className="flex items-center justify-center gap-2">
          {showIcon && <ShoppingBag size={16} />}
          <span>{buttonText}</span>
        </div>

        <AnimatePresence>
          {animate && (
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1.5, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="absolute inset-0 flex items-center justify-center pointer-events-none"
            >
              <ShoppingBag size={20} className="text-blue-500" />
            </motion.div>
          )}
        </AnimatePresence>
      </button>

      {/* Cart confirmation toast */}
      <AnimatedNotification 
        show={success} 
        type="cart" 
        isAdded={true} 
      />
    </>
  );
}