import { createContext, useContext, useState, useRef, useEffect } from 'react';
import { dataService } from '../services/dataService';
import { useAuth } from './AuthContext';
import { useNotification } from './NotificationContext';

// Define types as comments since JSX doesn't support TypeScript

// type CartItem = {
//   id: number;
//   name: string;
//   price: number;
//   color: string;
//   size: string;
//   quantity: number;
//   image: string;
// }

// type CartContextType = {
//   items: CartItem[];
//   addToCart: (item: Omit<CartItem, 'quantity'>, quantity?: number) => void;
//   removeFromCart: (id: number) => void;
//   updateQuantity: (id: number, quantity: number) => void;
//   clearCart: () => void;
//   totalItems: number;
//   subtotal: number;
// }

const CartContext = createContext(undefined);

export { CartContext };

export function CartProvider({ children }) {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const { user, isAuthenticated } = useAuth();
  const { warning } = useNotification();

  // Clear data immediately when user becomes null (logout)
  useEffect(() => {
    if (!user?.id) {
      setItems([]);
      setCurrentUserId(null);
    }
  }, [user?.id]);

  // Load cart from backend when user is authenticated
  useEffect(() => {
    const loadCart = async () => {
      // If user changed, clear previous user's data
      if (currentUserId && user?.id && currentUserId !== user.id) {
        setItems([]);
      }

      // Update current user ID
      setCurrentUserId(user?.id || null);

      if (!isAuthenticated || !user?.id) {
        // If not authenticated, clear cart data
        setItems([]);
        return;
      }

      try {
        setLoading(true);
        const cart = await dataService.getCart(user.id);

        // Transform backend cart items to frontend format
        const transformedItems = (cart.items || []).map(item => {
          const salePrice = item.sale_price ? parseFloat(item.sale_price) : null;
          const regularPrice = parseFloat(item.unit_price || item.price || 0);

          // Determine if item is on sale
          const isOnSale = salePrice !== null && salePrice > 0 && salePrice < regularPrice;

          return {
            id: item.product_id || item.id,
            name: item.product_name || item.name,
            price: regularPrice,
            salePrice: salePrice,
            sale_price: salePrice, // Also include snake_case for compatibility
            is_sale: isOnSale ? 1 : 0, // Add the is_sale flag
            color: item.selected_color || 'Default',
            size: item.selected_size || 'M',
            colorHex: item.selected_color_hex || '#000000',
            quantity: parseInt(item.quantity || 1),
            image: item.product_image || item.image,
            sku: item.product_sku || item.sku,
            outfitName: item.outfit_name || null,
            cartItemId: item.id, // Backend cart item ID for updates/deletes
            category: item.category || 'general'
          };
        });

        setItems(transformedItems);
      } catch (error) {
        console.error('Failed to load cart:', error);
        // Fallback to localStorage
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
          setItems(JSON.parse(savedCart));
        }
      } finally {
        setLoading(false);
      }
    };

    loadCart();
  }, [isAuthenticated, user?.id]);

  // Note: Removed localStorage backup to prevent data leakage between users

  const addToCart = async (product, quantity = 1) => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      warning('Please log in to add items to your cart', 5000);
      return false;
    }

    const itemKey = `${product.id}-${product.color}-${product.size}`;

    // Use sale price if available, otherwise use regular price
    const effectivePrice = (product.is_sale === 1 && product.sale_price) ? 
      parseFloat(product.sale_price) : 
      parseFloat(product.price);

    console.log('🛒 addToCart called with:', {
      productName: product.name,
      quantity,
      color: product.color,
      size: product.size,
      price: product.price,
      sale_price: product.sale_price,
      effectivePrice,
      outfitName: product.outfitName,
      itemKey,
      operationId: product.operationId,
      timestamp: new Date().toISOString()
    });

    if (isAuthenticated && user?.id) {
      try {
        // Prepare cart item for backend - Enhanced for rich product data
        const selectedColor = product.color || (product.colors && product.colors[0]?.name) || 'Default';
        const selectedColorHex = product.colorHex ||
          (product.colors && product.colors.find(c => c.name === selectedColor)?.value) || '#000000';
        const selectedSize = product.size || (product.sizes && (product.sizes[0]?.size_name || product.sizes[0])) || 'M';
        const productImage = product.image ||
          (product.colors && product.colors.find(c => c.name === selectedColor)?.images?.[0]) ||
          (product.images && product.images[0]) || null;

        const cartItem = {
          product_id: product.id,
          selected_color: selectedColor,
          selected_size: selectedSize,
          selected_color_hex: selectedColorHex,
          quantity: quantity,
          unit_price: parseFloat(product.price) || 0,
          sale_price: product.is_sale === 1 ? parseFloat(product.sale_price) : null,
          product_name: product.name,
          product_image: productImage,
          product_sku: product.sku || null,
          outfit_id: product.outfitId || null,
          outfit_name: product.outfitName || null
        };

        console.log('🔑 Calling dataService.addToCart with:', {
          userId: user.id,
          cartItem,
          timestamp: new Date().toISOString()
        });

        await dataService.addToCart(user.id, cartItem);

        console.log('✅ dataService.addToCart completed successfully');

        // Reload cart from backend to ensure frontend state is synchronized
        // This ensures we have the correct cartItemId and quantities
        try {
          const cart = await dataService.getCart(user.id);
          const transformedItems = (cart.items || []).map(item => {
            const salePrice = item.sale_price ? parseFloat(item.sale_price) : null;
            const regularPrice = parseFloat(item.unit_price || item.price || 0);

            // Determine if item is on sale
            const isOnSale = salePrice !== null && salePrice > 0 && salePrice < regularPrice;

            return {
              id: item.product_id || item.id,
              name: item.product_name || item.name,
              price: regularPrice,
              salePrice: salePrice,
              sale_price: salePrice, // Also include snake_case for compatibility
              is_sale: isOnSale ? 1 : 0, // Add the is_sale flag
              color: item.selected_color || 'Default',
              size: item.selected_size || 'M',
              colorHex: item.selected_color_hex || '#000000',
              quantity: parseInt(item.quantity || 1),
              image: item.product_image || item.image,
              sku: item.product_sku || item.sku,
              outfitName: item.outfit_name || null,
              cartItemId: item.id, // Backend cart item ID for updates/deletes
              category: item.category || 'general'
            };
          });
          setItems(transformedItems);
          console.log('🔄 Cart reloaded from backend after adding item');
        } catch (reloadError) {
          console.error('Failed to reload cart after adding item:', reloadError);
          // Fallback to local state update
          setItems((prevItems) => {
            const existingItemIndex = prevItems.findIndex(
              (item) =>
                item.id === product.id &&
                item.color === product.color &&
                item.size === product.size
            );

            let newItems;
            if (existingItemIndex > -1) {
              newItems = [...prevItems];
              newItems[existingItemIndex].quantity += quantity;
              // Ensure sale price is updated if it wasn't set before
              if (product.is_sale === 1 && product.sale_price && !newItems[existingItemIndex].salePrice) {
                newItems[existingItemIndex].salePrice = parseFloat(product.sale_price);
              }
            } else {
              newItems = [...prevItems, {
                ...product,
                price: parseFloat(product.price) || 0,
                salePrice: (product.is_sale === 1 && product.sale_price) ? parseFloat(product.sale_price) : null,
                quantity,
                outfitName: product.outfitName || null
              }];
            }
            return newItems;
          });
        }
        
        return true;
      } catch (error) {
        console.error('Failed to add to cart:', error);
        // Fallback to localStorage behavior
        setItems((prevItems) => {
          const existingItemIndex = prevItems.findIndex(
            (item) =>
              item.id === product.id &&
              item.color === product.color &&
              item.size === product.size
          );

          let newItems;
          if (existingItemIndex > -1) {
            newItems = [...prevItems];
            newItems[existingItemIndex].quantity += quantity;
          } else {
            newItems = [...prevItems, {
              ...product,
              price: parseFloat(product.price) || 0,
              salePrice: product.salePrice ? parseFloat(product.salePrice) : null,
              quantity,
              outfitName: product.outfitName || null
            }];
          }
          return newItems;
        });
        
        return true;
      }
    }
    
    return false;
  };

  const removeFromCart = async (id, color, size) => {
    if (isAuthenticated && user?.id) {
      try {
        // Find the item to get its backend ID
        const item = items.find(item =>
          item.id === id && item.color === color && item.size === size
        );

        if (item && item.cartItemId) {
          await dataService.removeFromCart(user.id, item.cartItemId);
        }

        setItems((prevItems) =>
          prevItems.filter(
            (item) =>
              !(item.id === id && item.color === color && item.size === size)
          )
        );
      } catch (error) {
        console.error('Failed to remove from cart:', error);
        // Fallback to localStorage behavior
        setItems((prevItems) =>
          prevItems.filter(
            (item) =>
              !(item.id === id && item.color === color && item.size === size)
          )
        );
      }
    } else {
      // Not authenticated, use localStorage
      setItems((prevItems) =>
        prevItems.filter(
          (item) =>
            !(item.id === id && item.color === color && item.size === size)
        )
      );
    }
  };

  const updateQuantity = async (id, quantity, color, size) => {
    if (isAuthenticated && user?.id) {
      try {
        // Find the item to get its backend ID
        const item = items.find(item =>
          item.id === id && item.color === color && item.size === size
        );

        if (item && item.cartItemId) {
          // Wait for backend update to complete before updating frontend state
          console.log('🔄 Updating cart quantity in backend:', {
            itemId: item.cartItemId,
            oldQuantity: item.quantity,
            newQuantity: Math.max(1, quantity)
          });

          await dataService.updateCartItem(user.id, item.cartItemId, Math.max(1, quantity));

          console.log('✅ Backend cart quantity updated successfully');
        }

        // Only update frontend state after backend update succeeds
        setItems((prevItems) =>
          prevItems.map((item) =>
            item.id === id && item.color === color && item.size === size
              ? { ...item, quantity: Math.max(1, quantity) }
              : item
          )
        );
      } catch (error) {
        console.error('Failed to update cart quantity:', error);
        // Don't update frontend state if backend update fails
        // This ensures frontend and backend stay in sync
        warning('Failed to update cart quantity. Please try again.', 3000);
      }
    } else {
      // Not authenticated, use localStorage
      setItems((prevItems) =>
        prevItems.map((item) =>
          item.id === id && item.color === color && item.size === size
            ? { ...item, quantity: Math.max(1, quantity) }
            : item
        )
      );
    }
  };

  const clearCart = async () => {
    if (isAuthenticated && user?.id) {
      try {
        await dataService.clearCart(user.id);
        setItems([]);
      } catch (error) {
        console.error('Failed to clear cart:', error);
        // Fallback to localStorage behavior
        setItems([]);
      }
    } else {
      // Not authenticated, use localStorage
      setItems([]);
    }
    // Always clear localStorage backup
    localStorage.removeItem('cart');
  };

  const totalItems = items.reduce((total, item) => total + item.quantity, 0);

  const subtotal = items.reduce(
    (total, item) => {
      // Use sale price if available, otherwise use regular price
      const effectivePrice = (item.is_sale === 1 && (item.sale_price || item.salePrice)) 
        ? parseFloat(item.sale_price || item.salePrice) 
        : parseFloat(item.price) || 0;
      return total + (effectivePrice * item.quantity);
    },
    0
  );
  
  console.log('🛒 Cart Subtotal Calculation:', {
    items: items.map(item => {
      const effectivePrice = (item.is_sale === 1 && (item.sale_price || item.salePrice))
        ? parseFloat(item.sale_price || item.salePrice)
        : parseFloat(item.price) || 0;
      return {
        id: item.id,
        name: item.name,
        price: item.price,
        sale_price: item.sale_price || item.salePrice,
        is_sale: item.is_sale,
        quantity: item.quantity,
        effectivePrice: effectivePrice,
        itemTotal: effectivePrice * item.quantity
      };
    }),
    subtotal
  });

  // Group items by outfit
  const getItemsByOutfit = () => {
    const outfits = {};
    const individualItems = [];

    items.forEach(item => {
      if (item.outfitName) {
        if (!outfits[item.outfitName]) {
          outfits[item.outfitName] = [];
        }
        outfits[item.outfitName].push(item);
      } else {
        individualItems.push(item);
      }
    });

    return { outfits, individualItems };
  };

  // Manual refresh function for cart
  const refreshCart = async () => {
    if (!isAuthenticated || !user?.id) return;

    try {
      setLoading(true);
      const cart = await dataService.getCart(user.id);

      // Transform backend cart items to frontend format
      const transformedItems = (cart.items || []).map(item => {
        const salePrice = item.sale_price ? parseFloat(item.sale_price) : null;
        const regularPrice = parseFloat(item.unit_price || item.price || 0);

        // Determine if item is on sale
        const isOnSale = salePrice !== null && salePrice > 0 && salePrice < regularPrice;

        return {
          id: item.product_id || item.id,
          name: item.product_name || item.name,
          price: regularPrice,
          salePrice: salePrice,
          sale_price: salePrice, // Also include snake_case for compatibility
          is_sale: isOnSale ? 1 : 0, // Add the is_sale flag
          color: item.selected_color || 'Default',
          size: item.selected_size || 'M',
          colorHex: item.selected_color_hex || '#000000',
          quantity: parseInt(item.quantity || 1),
          image: item.product_image || item.image,
          sku: item.product_sku || item.sku,
          outfitName: item.outfit_name || null,
          cartItemId: item.id, // Backend cart item ID for updates/deletes
          category: item.category || 'general'
        };
      });

      setItems(transformedItems);
      console.log('🔄 Cart manually refreshed');
      return transformedItems;
    } catch (error) {
      console.error('Failed to refresh cart:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <CartContext.Provider
      value={{
        items,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        totalItems,
        subtotal,
        getItemsByOutfit,
        refreshCart,
        loading,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};