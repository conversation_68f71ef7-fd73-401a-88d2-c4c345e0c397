import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  Edit3, 
  Phone, 
  Mail, 
  Calendar, 
  MapPin, 
  Heart, 
  ShoppingBag, 
  Package, 
  LogOut,
  Camera,
  Save,
  X,
  CheckCircle,
  Shirt,
  Eye,
  ArrowRight,
  Clock,
  Truck,
  CreditCard,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useWishlist } from '../context/WishlistContext';
import { useCart } from '../context/CartContext';
import { useOutfit } from '../context/OutfitContext';
import { useLogout } from '../hooks/useLogout';
import { dataService } from '../services/dataService';

const ProfilePage = () => {
  const { user, updateProfile, isLoading, refreshProfile } = useAuth();
  const { totalWishlistItems } = useWishlist();
  const { totalItems } = useCart();
  const { totalSavedOutfits } = useOutfit();
  const { handleCompleteLogout } = useLogout();
  const navigate = useNavigate();

  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    first_name: '',
    last_name: '',
    email: '',
    date_of_birth: '',
    gender: '',
    shipping_address: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'India'
    }
  });
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [recentOrders, setRecentOrders] = useState([]);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const [totalOrders, setTotalOrders] = useState(0);
  const [expandedOrders, setExpandedOrders] = useState(new Set());
  const [loadingOrderDetails, setLoadingOrderDetails] = useState(new Set());

  // Ref for edit form scrolling
  const editFormRef = useRef(null);

  // Initialize form with user data
  useEffect(() => {
    if (user) {
      console.log('DEBUG: User data for form initialization:', user);

      // Get shipping address from addresses array (default address or first address)
      let shippingAddress = {};
      if (user.addresses && Array.isArray(user.addresses) && user.addresses.length > 0) {
        // Find default address or use first address
        const defaultAddress = user.addresses.find(addr => addr.is_default === 1 || addr.is_default === true);
        shippingAddress = defaultAddress || user.addresses[0];
        console.log('DEBUG: Found shipping address:', shippingAddress);
      } else if (user.shipping_address) {
        // Fallback to shipping_address if addresses array not available
        shippingAddress = user.shipping_address;
      }

      setEditForm({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        date_of_birth: user.date_of_birth || '',
        gender: user.gender || '',
        shipping_address: {
          line1: shippingAddress.address_line_1 || shippingAddress.line1 || shippingAddress.street || '',
          line2: shippingAddress.address_line_2 || shippingAddress.line2 || '',
          city: shippingAddress.city || '',
          state: shippingAddress.state || '',
          postal_code: shippingAddress.postal_code || shippingAddress.zip || '',
          country: shippingAddress.country || 'India'
        }
      });
    }
  }, [user]);

  // Refresh profile on mount
  useEffect(() => {
    refreshProfile();
  }, []);

  // Fetch recent orders
  useEffect(() => {
    const fetchRecentOrders = async () => {
      if (!user) return;

      try {
        setOrdersLoading(true);
        const ordersRes = await dataService.getOrders(user.id);
        const ordersData = ordersRes.orders || ordersRes;
        const orders = Array.isArray(ordersData) ? ordersData : [];

        // Get detailed information for recent 3 orders
        const recentOrdersWithDetails = await Promise.all(
          orders.slice(0, 3).map(async (order) => {
            try {
              // Fetch detailed order information including items
              const detailedOrder = await dataService.getOrder(order.id);
              return {
                ...order,
                items: detailedOrder.items || [],
                shipping_address: detailedOrder.shipping_address || null,
                total_items: detailedOrder.items ? detailedOrder.items.length : order.total_items || 0
              };
            } catch (err) {
              console.error(`Failed to fetch details for order ${order.id}:`, err);
              // Return original order if detailed fetch fails
              return order;
            }
          })
        );

        setRecentOrders(recentOrdersWithDetails);
        setTotalOrders(orders.length);
      } catch (err) {
        console.error('Failed to fetch orders:', err);
      } finally {
        setOrdersLoading(false);
      }
    };

    fetchRecentOrders();
  }, [user]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setEditForm(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setEditForm(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Validate mandatory fields
  const validateMandatoryFields = () => {
    const errors = [];

    // Check mandatory fields
    if (!editForm.first_name?.trim()) errors.push('First Name is required');
    if (!editForm.last_name?.trim()) errors.push('Last Name is required');
    if (!editForm.email?.trim()) errors.push('Email is required');
    if (!editForm.gender) errors.push('Gender is required');
    if (!editForm.shipping_address.line1?.trim()) errors.push('Address Line 1 is required');
    if (!editForm.shipping_address.city?.trim()) errors.push('City is required');
    if (!editForm.shipping_address.state?.trim()) errors.push('State is required');
    if (!editForm.shipping_address.postal_code?.trim()) errors.push('Postal Code is required');
    if (!editForm.shipping_address.country?.trim()) errors.push('Country is required');

    // Validate email format
    if (editForm.email?.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(editForm.email.trim())) {
        errors.push('Please enter a valid email address');
      }
    }

    return errors;
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    // Validate mandatory fields first
    const validationErrors = validateMandatoryFields();
    if (validationErrors.length > 0) {
      alert('Please fill in all required fields:\n\n' + validationErrors.join('\n'));
      return;
    }

    // Prepare data in the format expected by backend
    const profileData = {
      first_name: editForm.first_name.trim(),
      last_name: editForm.last_name.trim(),
      email: editForm.email.trim(),
      date_of_birth: editForm.date_of_birth,
      gender: editForm.gender,
      // Convert shipping_address to address_data format expected by backend
      address_data: {
        first_name: editForm.first_name.trim(),
        last_name: editForm.last_name.trim(),
        address_line_1: editForm.shipping_address.line1.trim(),
        address_line_2: editForm.shipping_address.line2?.trim() || '',
        city: editForm.shipping_address.city.trim(),
        state: editForm.shipping_address.state.trim(),
        postal_code: editForm.shipping_address.postal_code.trim(),
        country: editForm.shipping_address.country.trim(),
        type: 'shipping',
        is_default: true
      }
    };

    console.log('DEBUG: Sending profile data:', profileData);

    const result = await updateProfile(profileData);

    if (result.success) {
      setIsEditing(false);
      setUpdateSuccess(true);
      setSuccessMessage('Profile updated successfully!');
      setTimeout(() => {
        setUpdateSuccess(false);
        setSuccessMessage('');
      }, 3000);
    } else {
      alert('Failed to update profile: ' + (result.error || 'Unknown error'));
    }
  };

  // Handle logout
  const handleLogout = async () => {
    await handleCompleteLogout('/');
  };

  // Toggle order details expansion
  const toggleOrderExpansion = (orderId) => {
    setExpandedOrders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(orderId)) {
        newSet.delete(orderId);
      } else {
        newSet.add(orderId);
      }
      return newSet;
    });
  };

  // Utility functions
  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatPrice = (price) => {
    return `₹${parseFloat(price || 0).toFixed(2)}`;
  };

  const getOrderStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return 'text-green-400 bg-green-500/10';
      case 'shipped':
      case 'out_for_delivery':
        return 'text-blue-400 bg-blue-500/10';
      case 'processing':
        return 'text-orange-400 bg-orange-500/10';
      case 'cancelled':
        return 'text-red-400 bg-red-500/10';
      default:
        return 'text-gray-400 bg-gray-500/10';
    }
  };

  const getOrderStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return CheckCircle;
      case 'shipped':
      case 'out_for_delivery':
        return Truck;
      case 'processing':
        return Clock;
      case 'cancelled':
        return X;
      default:
        return Package;
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-[#000000] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-gray-800 border-t-orange-500 rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-400 text-lg font-medium">Loading your profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#000000]">
      <div className="container mx-auto px-4 py-6 md:py-8 max-w-6xl">
        {/* Page Header */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8 md:mb-12"
        >
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-4">
            MY PROFILE
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl">
            Manage your account settings and view your order history
          </p>
        </motion.div> */}

        {/* Success Message */}
        <AnimatePresence>
          {updateSuccess && successMessage && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-[#1a1a1a] backdrop-blur-sm border border-green-500/30 rounded-xl text-green-400 flex items-center gap-3 shadow-xl"
              style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
            >
              <CheckCircle size={18} />
              <span className="font-medium">{successMessage}</span>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Profile Header Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-8 md:mb-12"
        >
          <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 border border-gray-800/60 shadow-xl"
            style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
          >
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6 md:gap-8">
              {/* Profile Image */}
              <div className="relative">
                <div className="w-20 h-20 md:w-24 md:h-24 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center overflow-hidden shadow-lg shadow-orange-500/20">
                  {user.profile_image ? (
                    <img
                      src={user.profile_image}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User size={32} className="text-white md:w-8 md:h-8" />
                  )}
                </div>

              </div>

              {/* Profile Info */}
              <div className="flex-1 space-y-4">
                <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4 md:gap-6">
                  <div className="space-y-2">
                    <h1 className="text-2xl md:text-3xl font-bold text-white">
                      {user.first_name} {user.last_name}
                    </h1>
                    <div className="flex items-center gap-2 text-gray-400">
                      <Mail size={16} />
                      <span className="text-sm md:text-base">{user.email}</span>
                    </div>
                    {user.phone && (
                      <div className="flex items-center gap-2 text-gray-400">
                        <Phone size={16} />
                        <span className="text-sm md:text-base">{user.phone}</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        setIsEditing(true);
                        // Scroll to edit form after a short delay to allow form to render
                        setTimeout(() => {
                          editFormRef.current?.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start',
                            inline: 'nearest'
                          });
                        }, 100);
                      }}
                      className="flex items-center gap-2 px-4 py-2 bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-xl font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 shadow-lg shadow-orange-500/20"
                    >
                      <Edit3 size={16} />
                      Edit Profile
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleLogout}
                      className="flex items-center gap-2 px-4 py-2 bg-gray-800/60 text-gray-300 rounded-xl font-medium hover:bg-gray-700/60 hover:text-white transition-all duration-300 border border-gray-700/30"
                    >
                      <LogOut size={16} />
                      Logout
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Edit Profile Form */}
        <AnimatePresence>
          {isEditing && (
            <motion.div
              ref={editFormRef}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-8 md:mb-12"
            >
              <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 border border-gray-800/60 shadow-xl"
                style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
              >
                <div className="flex items-center justify-between mb-6 md:mb-8">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-orange-500/10 rounded-xl flex items-center justify-center"
                      style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}
                    >
                      <Edit3 size={20} className="text-orange-400 md:w-6 md:h-6" />
                    </div>
                    <h2 className="text-xl md:text-2xl font-bold text-white">Edit Profile</h2>
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsEditing(false)}
                    className="w-8 h-8 bg-gray-800/60 rounded-lg flex items-center justify-center hover:bg-gray-700/60 transition-colors border border-gray-700/30"
                  >
                    <X size={16} className="text-gray-400" />
                  </motion.button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white mb-4">Personal Information</h3>

                    <div>
                      <label className="block text-gray-400 text-sm font-medium mb-2">
                        First Name <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        value={editForm.first_name}
                        onChange={(e) => handleInputChange('first_name', e.target.value)}
                        className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                        placeholder="Enter your first name"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm font-medium mb-2">
                        Last Name <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        value={editForm.last_name}
                        onChange={(e) => handleInputChange('last_name', e.target.value)}
                        className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                        placeholder="Enter your last name"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm font-medium mb-2">
                        Email <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="email"
                        value={editForm.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                        placeholder="Enter your email"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm font-medium mb-2">Date of Birth</label>
                      <input
                        type="date"
                        value={editForm.date_of_birth}
                        onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                        className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm font-medium mb-2">
                        Gender <span className="text-red-400">*</span>
                      </label>
                      <select
                        value={editForm.gender}
                        onChange={(e) => handleInputChange('gender', e.target.value)}
                        className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                        required
                      >
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white mb-4">Shipping Address</h3>

                    <div>
                      <label className="block text-gray-400 text-sm font-medium mb-2">
                        Address Line 1 <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        value={editForm.shipping_address.line1}
                        onChange={(e) => handleInputChange('shipping_address.line1', e.target.value)}
                        className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                        placeholder="Street address"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm font-medium mb-2">Address Line 2</label>
                      <input
                        type="text"
                        value={editForm.shipping_address.line2}
                        onChange={(e) => handleInputChange('shipping_address.line2', e.target.value)}
                        className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                        placeholder="Apartment, suite, etc. (optional)"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-400 text-sm font-medium mb-2">
                          City <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          value={editForm.shipping_address.city}
                          onChange={(e) => handleInputChange('shipping_address.city', e.target.value)}
                          className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                          placeholder="City"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-gray-400 text-sm font-medium mb-2">
                          State <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          value={editForm.shipping_address.state}
                          onChange={(e) => handleInputChange('shipping_address.state', e.target.value)}
                          className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                          placeholder="State"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-400 text-sm font-medium mb-2">
                          Postal Code <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          value={editForm.shipping_address.postal_code}
                          onChange={(e) => handleInputChange('shipping_address.postal_code', e.target.value)}
                          className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                          placeholder="Postal code"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-gray-400 text-sm font-medium mb-2">
                          Country <span className="text-red-400">*</span>
                        </label>
                        <select
                          value={editForm.shipping_address.country}
                          onChange={(e) => handleInputChange('shipping_address.country', e.target.value)}
                          className="w-full px-4 py-3 bg-gray-800/60 border border-gray-700/60 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                          required
                        >
                          <option value="India">India</option>
                          <option value="USA">USA</option>
                          <option value="UK">UK</option>
                          <option value="Canada">Canada</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Save Button */}
                <div className="flex justify-end gap-4 mt-8">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setIsEditing(false)}
                    className="px-6 py-3 bg-gray-800/60 text-gray-300 rounded-xl font-medium hover:bg-gray-700/60 hover:text-white transition-all duration-300 border border-gray-700/30"
                  >
                    Cancel
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleSaveProfile}
                    disabled={isLoading}
                    className="flex items-center gap-2 px-6 py-3 bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-xl font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 shadow-lg shadow-orange-500/20 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                        Save Changes
                      </>
                    )}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8 md:mb-12"
        >
          {[
            { icon: Heart, value: totalWishlistItems, label: 'Wishlist', color: 'from-red-500 to-pink-500', onClick: () => navigate('/wishlist') },
            { icon: ShoppingBag, value: totalItems, label: 'Cart Items', color: 'from-blue-500 to-cyan-500', onClick: () => navigate('/cart') },
            { icon: Package, value: totalOrders, label: 'Orders', color: 'from-green-500 to-emerald-500', onClick: () => navigate('/orders') },
            { icon: Shirt, value: totalSavedOutfits, label: 'Outfits', color: 'from-purple-500 to-violet-500', onClick: () => navigate('/outfits') }
          ].map((stat, idx) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + idx * 0.1 }}
              whileHover={{ y: -4, scale: 1.02 }}
              onClick={stat.onClick}
              className="cursor-pointer group"
            >
              <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl md:rounded-2xl p-4 md:p-6 border border-gray-800/60 hover:border-orange-500/30 transition-all duration-300 shadow-xl text-center"
                style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
              >
                <div className="w-12 h-12 md:w-14 md:h-14 bg-orange-500/10 rounded-xl flex items-center justify-center mx-auto mb-3 md:mb-4 group-hover:bg-orange-500/20 transition-colors"
                  style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}
                >
                  <stat.icon size={20} className="text-orange-400 md:w-6 md:h-6" />
                </div>
                <p className="text-2xl md:text-3xl font-bold text-white mb-1">{stat.value}</p>
                <p className="text-gray-400 text-sm md:text-base font-medium">{stat.label}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Recent Orders Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mb-8 md:mb-12"
        >
          <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 border border-gray-800/60 shadow-xl"
            style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
          >
            <div className="flex items-center justify-between mb-6 md:mb-8">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 md:w-12 md:h-12 bg-orange-500/10 rounded-xl flex items-center justify-center"
                  style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}
                >
                  <Package size={20} className="text-orange-400 md:w-6 md:h-6" />
                </div>
                <h2 className="text-xl md:text-2xl font-bold text-white">Recent Orders</h2>
              </div>

              {totalOrders > 3 && (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => navigate('/orders')}
                  className="flex items-center gap-2 px-4 py-2  text-gray-300 rounded-xl font-medium  hover:text-white transition-all duration-300 "
                >
                  View All
                  <ArrowRight size={16} />
                </motion.button>
              )}
            </div>

            {ordersLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-gray-800/30 rounded-xl p-4 animate-pulse">
                    <div className="h-4 bg-gray-700/50 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-700/50 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : recentOrders.length > 0 ? (
              <div className="space-y-4">
                {recentOrders.map((order, index) => {
                  const StatusIcon = getOrderStatusIcon(order.status);
                  return (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index }}
                      className=" rounded-xl p-4 md:p-5 border border-gray-700/30 hover:border-orange-500/30 transition-all duration-300"
                    >
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3 md:gap-4">
                        <div className="flex items-start gap-3 md:gap-4">
                          <div className="w-10 h-10 md:w-12 md:h-12  rounded-xl flex items-center justify-center">
                            <StatusIcon size={18} className="text-gray-400 md:w-5 md:h-5" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h3 className="text-white font-semibold text-sm md:text-base mb-1">
                              Order #{order.order_number}
                            </h3>
                            <p className="text-gray-400 text-xs md:text-sm mb-2">
                              {formatDate(order.created_at)} • {order.total_items} item{order.total_items !== 1 ? 's' : ''}
                            </p>
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getOrderStatusColor(order.status)}`}>
                                {order.status?.replace('_', ' ').toUpperCase()}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between md:justify-end gap-4">
                          <span className="text-white font-bold text-lg">
                            {formatPrice(order.total_amount)}
                          </span>
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => toggleOrderExpansion(order.id)}
                            className="w-8 h-8 bg-orange-500/10 rounded-lg flex items-center justify-center hover:bg-orange-500/20 transition-colors"
                            style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}
                          >
                            {expandedOrders.has(order.id) ? (
                              <ChevronUp size={14} className="text-orange-400" />
                            ) : (
                              <ChevronDown size={14} className="text-orange-400" />
                            )}
                          </motion.button>
                        </div>
                      </div>

                      {/* Order Details Dropdown */}
                      <AnimatePresence>
                        {expandedOrders.has(order.id) && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="mt-4 pt-4 border-t border-gray-700/30"
                          >
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                              {/* Order Items */}
                              <div>
                                <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                                  <Package size={16} className="text-orange-400" />
                                  Items Purchased
                                </h4>
                                <div className="space-y-2">
                                  {order.items && order.items.length > 0 ? (
                                    order.items.map((item, itemIndex) => (
                                      <div key={itemIndex} className=" rounded-lg p-3 flex items-start gap-3">
                                        {/* Product Image */}
                                        <div className="w-12 h-12 md:w-16 md:h-16 bg-gray-700/50 rounded-lg overflow-hidden flex-shrink-0">
                                          {item.product_image ? (
                                            <img
                                              src={item.product_image}
                                              alt={item.product_name || 'Product'}
                                              className="w-full h-full object-cover"
                                              onError={(e) => {
                                                e.target.style.display = 'none';
                                                e.target.nextSibling.style.display = 'flex';
                                              }}
                                            />
                                          ) : null}
                                          <div className={`w-full h-full ${item.product_image ? 'hidden' : 'flex'} items-center justify-center`}>
                                            <Shirt size={16} className="text-gray-500" />
                                          </div>
                                        </div>

                                        {/* Product Details */}
                                        <div className="flex-1 min-w-0">
                                          <p className="text-white text-sm font-medium truncate">{item.product_name || item.name || 'Unknown Product'}</p>
                                          <div className="text-gray-400 text-xs mt-1 space-y-1">
                                            {item.selected_color && (
                                              <p>Color: <span className="text-gray-300">{item.selected_color}</span></p>
                                            )}
                                            {item.selected_size && (
                                              <p>Size: <span className="text-gray-300">{item.selected_size}</span></p>
                                            )}
                                            {item.quantity && (
                                              <p>Quantity: <span className="text-gray-300">{item.quantity}</span></p>
                                            )}
                                          </div>
                                        </div>

                                        {/* Price */}
                                        <div className="text-right flex-shrink-0">
                                          <span className="text-orange-400 font-semibold text-sm">
                                            {formatPrice(item.total_price || item.unit_price || 0)}
                                          </span>
                                          {item.quantity > 1 && (
                                            <p className="text-gray-400 text-xs mt-1">
                                              {formatPrice(item.unit_price || 0)} each
                                            </p>
                                          )}
                                        </div>
                                      </div>
                                    ))
                                  ) : (
                                    <div className=" rounded-lg p-4 text-center">
                                      <Package size={24} className="text-gray-500 mx-auto mb-2" />
                                      <p className="text-gray-400 text-sm">No item details available</p>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Order Details */}
                              <div className="space-y-4">
                                {/* Shipping Address */}
                                <div>
                                  <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                                    <Truck size={16} className="text-orange-400" />
                                    Shipping Address
                                  </h4>
                                  <div className=" rounded-lg p-3">
                                    {order.shipping_address && (order.shipping_address.address_line_1 || order.shipping_address.city) ? (
                                      <div className="text-gray-300 text-sm space-y-1">
                                        {(order.shipping_address.first_name || order.shipping_address.last_name) && (
                                          <p className="font-medium">
                                            {order.shipping_address.first_name} {order.shipping_address.last_name}
                                          </p>
                                        )}
                                        {order.shipping_address.address_line_1 && (
                                          <p>{order.shipping_address.address_line_1}</p>
                                        )}
                                        {order.shipping_address.address_line_2 && (
                                          <p>{order.shipping_address.address_line_2}</p>
                                        )}
                                        {(order.shipping_address.city || order.shipping_address.state || order.shipping_address.postal_code) && (
                                          <p>
                                            {order.shipping_address.city}
                                            {order.shipping_address.city && order.shipping_address.state && ', '}
                                            {order.shipping_address.state} {order.shipping_address.postal_code}
                                          </p>
                                        )}
                                        {order.shipping_address.country && (
                                          <p>{order.shipping_address.country}</p>
                                        )}
                                      </div>
                                    ) : (
                                      <div className="text-center py-2">
                                        <Truck size={20} className="text-gray-500 mx-auto mb-1" />
                                        <p className="text-gray-400 text-sm">No shipping address available</p>
                                      </div>
                                    )}
                                  </div>
                                </div>

                                {/* Payment & Total */}
                                <div>
                                  <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                                    <CreditCard size={16} className="text-orange-400" />
                                    Payment Details
                                  </h4>
                                  <div className=" rounded-lg p-3 space-y-2">
                                    <div className="flex justify-between items-center">
                                      <span className="text-gray-400 text-sm">Payment Status:</span>
                                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                                        order.payment_status === 'paid' ? 'bg-green-500/20 text-green-400' :
                                        order.payment_status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                                        'bg-red-500/20 text-red-400'
                                      }`}>
                                        {order.payment_status?.toUpperCase() || 'UNKNOWN'}
                                      </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <span className="text-gray-400 text-sm">Payment Method:</span>
                                      <span className="text-white text-sm">{order.payment_method || 'Not specified'}</span>
                                    </div>
                                    <div className="border-t border-gray-700/30 pt-2 mt-2">
                                      <div className="flex justify-between items-center">
                                        <span className="text-white font-semibold">Total Amount:</span>
                                        <span className="text-orange-400 font-bold text-lg">{formatPrice(order.total_amount)}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 md:py-12">
                <div className="w-16 h-16 md:w-20 md:h-20  rounded-full flex items-center justify-center mx-auto mb-4">
                  <Package size={24} className="text-gray-500 md:w-8 md:h-8" />
                </div>
                <h3 className="text-white font-semibold text-lg md:text-xl mb-2">No Orders Yet</h3>
                <p className="text-gray-400 mb-6 max-w-md mx-auto">
                  Start shopping to see your orders here
                </p>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => navigate('/collections')}
                  className="px-6 py-3 bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-xl font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 shadow-lg shadow-orange-500/20"
                >
                  Start Shopping
                </motion.button>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ProfilePage;
