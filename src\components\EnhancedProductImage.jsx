import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, ZoomIn, Eye } from 'lucide-react';
import EnhancedImageZoom from './EnhancedImageZoom';
import EnhancedImageTransitions from './EnhancedImageTransitions';
import InteractiveHotspots from './InteractiveHotspots';

export default function EnhancedProductImage({
  product,
  selectedColorIndex = 0,
  currentImageIndex = 0,
  onImageChange,
  onColorChange,
  className = '',
  showHotspots = true,
  enableZoom = true,
  enableTransitions = true,
  viewMode = 'card' // 'card' or 'page'
}) {
  const [isHovered, setIsHovered] = useState(false);
  const [showZoomMode, setShowZoomMode] = useState(false);
  const [preloadedImages, setPreloadedImages] = useState(new Set());

  // Get current images based on selected color
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();
  const currentImage = currentImages[currentImageIndex] || currentImages[0];

  // Preload images for better performance
  useEffect(() => {
    const preloadImages = () => {
      currentImages.forEach((src, index) => {
        if (!preloadedImages.has(src)) {
          const img = new Image();
          img.onload = () => {
            setPreloadedImages(prev => new Set([...prev, src]));
          };
          img.src = src;
        }
      });
    };

    if (currentImages.length > 0) {
      preloadImages();
    }
  }, [currentImages, preloadedImages]);

  const handleImageChange = (newIndex) => {
    if (onImageChange) {
      onImageChange(newIndex);
    }
  };

  const handleColorChange = (colorIndex) => {
    if (onColorChange) {
      onColorChange(colorIndex);
    }
  };

  const nextImage = () => {
    const newIndex = currentImageIndex < currentImages.length - 1 ? currentImageIndex + 1 : 0;
    handleImageChange(newIndex);
  };

  const prevImage = () => {
    const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : currentImages.length - 1;
    handleImageChange(newIndex);
  };

  // Sample hotspots for product features
  const productHotspots = [
    {
      id: 1,
      x: 30,
      y: 25,
      type: 'material',
      title: 'Premium Fabric',
      description: `${product.name} features high-quality materials for comfort and durability`,
      icon: Eye,
      color: 'blue'
    },
    {
      id: 2,
      x: 70,
      y: 40,
      type: 'feature',
      title: 'Quality Stitching',
      description: 'Reinforced seams and professional finishing for long-lasting wear',
      icon: ZoomIn,
      color: 'green'
    }
  ];

  if (viewMode === 'page') {
    // Full product page view with all features
    return (
      <div className={`relative ${className}`}>
        {enableTransitions ? (
          <div className="relative">
            <EnhancedImageTransitions
              images={currentImages}
              currentIndex={currentImageIndex}
              onIndexChange={handleImageChange}
              transitionType="fade"
              className="w-full h-full"
              imageClassName="group-hover:scale-105 transition-transform duration-700"
              enableSwipe={true}
            />
            
            {/* Enhanced Zoom Overlay */}
            {enableZoom && (
              <div className="absolute inset-0">
                <EnhancedImageZoom
                  src={currentImage}
                  alt={`${product.name} - Image ${currentImageIndex + 1}`}
                  className="w-full h-full"
                  zoomLevel={3}
                  enableMagnifier={true}
                  showControls={true}
                  highResSrc={currentImage} // In real app, this would be a higher resolution version
                />
              </div>
            )}

            {/* Interactive Hotspots */}
            {showHotspots && (
              <InteractiveHotspots
                hotspots={productHotspots}
                className="absolute inset-0"
                showOnHover={true}
                alwaysVisible={false}
              />
            )}
          </div>
        ) : (
          <div className="relative w-full h-full">
            <img
              src={currentImage && currentImage.includes('cloudinary.com')
                ? currentImage.replace('/upload/', '/upload/w_600,h_600,c_fill,f_auto,q_auto/')
                : currentImage}
              alt={`${product.name} - Image ${currentImageIndex + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        )}
      </div>
    );
  }

  // Card view for product listings
  return (
    <motion.div
      className={`relative group overflow-hidden ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      {/* Main Image with Transitions */}
      {enableTransitions ? (
        <EnhancedImageTransitions
          images={currentImages}
          currentIndex={currentImageIndex}
          onIndexChange={handleImageChange}
          transitionType="fade"
          className="w-full h-full"
          imageClassName="group-hover:scale-105 transition-transform duration-700"
          showControls={currentImages.length > 1}
          showDots={currentImages.length > 1}
          enableSwipe={true}
        />
      ) : (
        <div className="relative w-full h-full">
          <AnimatePresence mode="wait">
            <motion.img
              key={currentImageIndex}
              src={(() => {
                // Optimize Cloudinary URLs if they exist
                return currentImage && currentImage.includes('cloudinary.com')
                  ? currentImage.replace('/upload/', '/upload/w_400,h_400,c_fill,f_auto,q_auto/')
                  : currentImage;
              })()}
              alt={`${product.name} - ${product.colors[selectedColorIndex]?.name || 'Default'} - Image ${currentImageIndex + 1}`}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              onError={(e) => {
                e.target.src = '/api/placeholder/400/400';
              }}
            />
          </AnimatePresence>

          {/* Navigation Arrows */}
          {currentImages.length > 1 && (
            <>
              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: isHovered ? 1 : 0 }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  prevImage();
                }}
                className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/60 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-all z-20"
              >
                <ChevronLeft size={16} />
              </motion.button>

              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: isHovered ? 1 : 0 }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  nextImage();
                }}
                className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/60 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-all z-20"
              >
                <ChevronRight size={16} />
              </motion.button>
            </>
          )}

          {/* Image Dots */}
          {currentImages.length > 1 && (
            <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-1.5 z-20">
              {currentImages.map((_, idx) => (
                <button
                  key={idx}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleImageChange(idx);
                  }}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    idx === currentImageIndex
                      ? 'bg-white scale-110'
                      : 'bg-white/50 hover:bg-white/75'
                  }`}
                />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Enhanced Zoom on Hover (Card View) */}
      {enableZoom && isHovered && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute top-2 right-2 z-30"
        >
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="w-8 h-8 bg-black/70 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-colors"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setShowZoomMode(true);
            }}
          >
            <ZoomIn size={14} />
          </motion.button>
        </motion.div>
      )}

      {/* Interactive Hotspots (Card View) */}
      {showHotspots && isHovered && (
        <InteractiveHotspots
          hotspots={productHotspots}
          className="absolute inset-0"
          showOnHover={true}
          alwaysVisible={false}
        />
      )}

      {/* Color Indicator */}
      {product.colors && product.colors.length > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 10 }}
          className="absolute top-3 left-3 bg-black/70 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm z-20"
        >
          {product.colors[selectedColorIndex]?.name || 'Default'}
        </motion.div>
      )}

      {/* Gradient Overlay */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isHovered ? 1 : 0 }}
        className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none"
      />

      {/* Zoom Modal */}
      <AnimatePresence>
        {showZoomMode && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setShowZoomMode(false)}
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              className="relative max-w-4xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <EnhancedImageZoom
                src={currentImage}
                alt={`${product.name} - Zoomed View`}
                className="w-full h-full max-h-[80vh]"
                zoomLevel={4}
                enableMagnifier={false}
                showControls={true}
                highResSrc={currentImage}
              />
              <button
                onClick={() => setShowZoomMode(false)}
                className="absolute top-4 right-4 w-10 h-10 bg-black/70 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-colors"
              >
                ×
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
