import { useState, useEffect, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import ImageLightbox from '../components/ImageLightbox';
import {
  Check,
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  Heart,
  Share2,
  Star,
  StarHalf,
  MessageCircle,
  ChevronDown,
  Tag,
  Truck,
  RefreshCw,
  RotateCcw,
  ShieldCheck,
  Info,
  ThumbsUp,
  Copy,
  ShoppingCart,
  Wand2,
  Shirt,
  Sparkles
} from 'lucide-react';
import AnimatedNotification from '../components/ui/AnimatedNotification';

import {
  FacebookShareButton,
  TwitterShareButton,
  WhatsappShareButton,
  FacebookIcon,
  TwitterIcon,
  WhatsappIcon
} from 'react-share';
import { dataService } from '../services/dataService';
import { reviewAPI } from '../services/reviewAPI';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import { useAuth } from '../context/AuthContext';
import { useNotification } from '../context/NotificationContext';
import WishlistButton from '../components/WishlistButton';
import SizeChart from '../components/SizeChart';
import ProductInfo from '../components/ProductInfo';
import SimilarProducts from '../components/SimilarProducts';
import ProductCartButton from '../components/ProductCartButton';

import QuickAddToOutfit from '../components/QuickAddToOutfit';
import CustomOutfitButton from '../components/CustomOutfitButton';
import ReviewModal from '../components/ReviewModal';




const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.4 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

// Stars component for ratings
const RatingStars = ({ rating }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  return (
    <div className="flex">
      {[...Array(5)].map((_, i) => {
        if (i < fullStars) {
          return <Star key={i} size={18} className="fill-yellow-400 text-yellow-400" />;
        } else if (i === fullStars && hasHalfStar) {
          return <StarHalf key={i} size={18} className="fill-yellow-400 text-yellow-400" />;
        } else {
          return <Star key={i} size={18} className="text-gray-500" />;
        }
      })}
    </div>
  );
};

export default function ProductPage() {
  const { id } = useParams();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { user, isAuthenticated } = useAuth();
  const { success, error, warning, info } = useNotification();

  const [currentImage, setCurrentImage] = useState(0);
  const [selectedColor, setSelectedColor] = useState(null);
  const [selectedSize, setSelectedSize] = useState('');
  const [activeTab, setActiveTab] = useState('description');
  const [showSizeChart, setShowSizeChart] = useState(false);

  const [showReviewForm, setShowReviewForm] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [loadingCart, setLoadingCart] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Reviews state
  const [reviews, setReviews] = useState([]);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [reviewStats, setReviewStats] = useState({
    average_rating: 0,
    total_reviews: 0,
    rating_distribution: [0, 0, 0, 0, 0]
  });
  const [userHasReviewed, setUserHasReviewed] = useState(false);
  const [userReviewLoading, setUserReviewLoading] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [reviewsPerPage] = useState(5);

  // Helpful votes state
  const [votingStates, setVotingStates] = useState({}); // Track voting states for each review
  const [userVotes, setUserVotes] = useState({}); // Track which reviews user has voted on

  // Touch/swipe functionality for mobile navigation
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [cartAdded, setCartAdded] = useState(false);
  const [wishlistAction, setWishlistAction] = useState(null);
  const [showWishlistNotification, setShowWishlistNotification] = useState(false);

  // Image lightbox state
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxImages, setLightboxImages] = useState([]);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [lightboxReviewTitle, setLightboxReviewTitle] = useState('');
  const [lightboxReviewerName, setLightboxReviewerName] = useState('');


  const lastAddToCartTime = useRef(0);

  const isWishlisted = isInWishlist(product?.id);

  const handleWishlistToggle = () => {
    if (isWishlisted) {
      removeFromWishlist(product.id);
      setWishlistAction('removed');
    } else {
      addToWishlist({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.images[0],
        description: product.description
      });
      setWishlistAction('added');
    }
    
    // Show notification
    setShowWishlistNotification(true);
    
    // Hide notification after 2 seconds
    setTimeout(() => {
      setShowWishlistNotification(false);
    }, 2000);
  };



  const shareUrl = window.location.href;

  // Copy link functionality
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopySuccess(true);
      setTimeout(() => {
        setCopySuccess(false);
        setShowShareMenu(false);
      }, 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = shareUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess(true);
      setTimeout(() => {
        setCopySuccess(false);
        setShowShareMenu(false);
      }, 2000);
    }
  };

  // Close share menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showShareMenu && !event.target.closest('.share-menu-container')) {
        setShowShareMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showShareMenu]);

  // Load product from API
  useEffect(() => {
    const loadProduct = async () => {
      if (!id) return;

      setLoading(true);
      try {
        const productData = await dataService.getProduct(id);
        console.log('📦 Raw product data from API:', JSON.parse(JSON.stringify(productData)));
        
        // Log all product properties to check for sale-related fields
        console.log('🔍 All product properties:', Object.keys(productData).join(', '));
        
        // Log specific price-related properties
        console.log('💰 Price-related properties:', {
          price: productData.price,
          sale_price: productData.sale_price,
          salePrice: productData.salePrice,
          is_sale: productData.is_sale,
          on_sale: productData.on_sale,
          // Check for any other price-related fields
          ...Object.entries(productData).reduce((acc, [key, value]) => {
            if (key.toLowerCase().includes('price') || key.toLowerCase().includes('sale')) {
              acc[key] = value;
            }
            return acc;
          }, {})
        });
        
        setProduct({
          ...productData,
          // Ensure price and sale_price are numbers
          price: parseFloat(productData.price) || 0,
          sale_price: productData.sale_price ? parseFloat(productData.sale_price) : null,
          // Ensure is_sale is a proper boolean
          is_sale: productData.is_sale === 1 || productData.is_sale === true ? 1 : 0
        });
      } catch (error) {
        console.error('Failed to load product:', error);
        setProduct(null);
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [id]);

  // Scroll to top when component mounts or product changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [id]);

  useEffect(() => {
    if (product) {
      setSelectedColor(product.colors?.[0] || null);
      // Set first available size as default, or first size if none available
      const availableSize = product.sizes?.find(size => size.is_available === 1);
      const firstSize = product.sizes?.[0];
      setSelectedSize(availableSize?.size_name || firstSize?.size_name || '');
      setCurrentImage(0);
      // Load reviews for this product
      loadReviews(product.id);
      // Check if user has already reviewed this product
      checkUserReview(product.id);
    }
  }, [product]);

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="text-center"
        >
          <div className="relative">
            <div className="w-12 h-12 border-4 border-blue-600/20 border-t-blue-600 rounded-full animate-spin mx-auto mb-4" />
            <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-cyan-600 rounded-full animate-spin animate-reverse mx-auto" style={{ animationDelay: '0.5s' }} />
          </div>
          <p className="text-gray-400 text-lg">Loading product...</p>
        </motion.div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 md:px-8 py-16 text-center">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
        >
          <h2 className="text-2xl text-white mb-4">Product not found</h2>
          <Link to="/" className="text-indigo-500 hover:text-indigo-400">
            Return to home page
          </Link>
        </motion.div>
      </div>
    );
  }

  // Get current images based on selected color (like Burberry)
  const getCurrentImages = () => {
    if (selectedColor && selectedColor.images && selectedColor.images.length > 0) {
      return selectedColor.images;
    }
    return product.images || [];
  };

  const handleColorChange = (color) => {
    setSelectedColor(color);
    // Reset to first image when color changes (like Burberry)
    setCurrentImage(0);
  };

  const handleAddToCart = () => {
    console.log('🔥 handleAddToCart called at:', new Date().toISOString());
    console.log('🔥 Current state:', { 
      selectedColor: selectedColor?.name, 
      selectedSize, 
      loadingCart, 
      cartAdded,
      product: {
        id: product.id,
        is_sale: product.is_sale,
        price: product.price,
        sale_price: product.sale_price
      }
    });

    if (!selectedColor || !selectedSize || loadingCart || cartAdded) {
      console.log('❌ Early return due to validation');
      return;
    }

    // Prevent duplicate calls within 1 second (React StrictMode protection)
    const now = Date.now();
    if (now - lastAddToCartTime.current < 1000) {
      console.log('❌ Blocked duplicate call within 1 second');
      return;
    }
    lastAddToCartTime.current = now;

    console.log('✅ Proceeding with add to cart');
    setLoadingCart(true);
    setCartAdded(false);

    // Create the product object with all necessary fields
    const cartItem = {
      id: product.id,
      name: product.name,
      price: parseFloat(product.price) || 0, // Always pass the original price
      sale_price: (product.is_sale === 1 && product.sale_price) ? parseFloat(product.sale_price) : null,
      is_sale: product.is_sale === 1 ? 1 : 0,
      color: selectedColor.name,
      size: selectedSize,
      image: selectedColor.images?.[0] || product.images?.[0],
      sku: product.sku,
      category: product.category,
      // Add any other required fields
    };

    console.log('🛒 Adding to cart:', cartItem);

    // Simulate network request
    setTimeout(() => {
      const result = addToCart(cartItem, 1); // Default quantity of 1 for clothing

      setLoadingCart(false);
      
      // Only show success state if cart addition was successful
      if (result !== false) {
        setCartAdded(true);

        // Reset the "Added" state after 2 seconds
        setTimeout(() => {
          setCartAdded(false);
        }, 2000);
      }
    }, 800);
  };

  // Swipe functionality for mobile navigation
  const minSwipeDistance = 50;

  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && getCurrentImages().length > 1) {
      // Swipe left - next image
      setCurrentImage(prev => (prev + 1) % getCurrentImages().length);
    }

    if (isRightSwipe && getCurrentImages().length > 1) {
      // Swipe right - previous image
      setCurrentImage(prev => prev === 0 ? getCurrentImages().length - 1 : prev - 1);
    }
  };

  // Navigation handlers for desktop arrows
  const handlePrevImage = () => {
    setCurrentImage(prev => prev === 0 ? getCurrentImages().length - 1 : prev - 1);
  };

  const handleNextImage = () => {
    setCurrentImage(prev => (prev + 1) % getCurrentImages().length);
  };



  // Load reviews function
  const loadReviews = async (productId, page = 1) => {
    try {
      setReviewsLoading(true);
      const response = await reviewAPI.getProductReviews(productId, page, reviewsPerPage);
      const reviewsList = response.reviews || [];
      setReviews(reviewsList);
      setReviewStats(response.stats || {
        average_rating: 0,
        total_reviews: 0,
        rating_distribution: [0, 0, 0, 0, 0]
      });

      // Update pagination info
      if (response.pagination) {
        setCurrentPage(response.pagination.page);
        setTotalPages(response.pagination.pages);
      }

      // Load user votes for the reviews
      await loadUserVotes(reviewsList);
    } catch (error) {
      console.error('Failed to load reviews:', error);
      setReviews([]);
    } finally {
      setReviewsLoading(false);
    }
  };

  const checkUserReview = async (productId) => {
    try {
      setUserReviewLoading(true);
      const result = await reviewAPI.checkUserReview(productId);
      setUserHasReviewed(result.hasReviewed);
    } catch (error) {
      console.error('Failed to check user review:', error);
      // On error, assume user hasn't reviewed to be safe
      setUserHasReviewed(false);
    } finally {
      setUserReviewLoading(false);
    }
  };

  // Load user votes for all reviews
  const loadUserVotes = async (reviewsList) => {
    if (!isAuthenticated || !reviewsList.length) return;

    try {
      const votePromises = reviewsList.map(review =>
        reviewAPI.checkUserVote(review.id)
      );

      const voteResults = await Promise.all(votePromises);

      const votesMap = {};
      reviewsList.forEach((review, index) => {
        const voteResult = voteResults[index];
        if (voteResult.hasVoted) {
          votesMap[review.id] = voteResult.vote;
        }
      });

      setUserVotes(votesMap);
    } catch (error) {
      console.error('Failed to load user votes:', error);
    }
  };

  // Handle helpful vote
  const handleHelpfulVote = async (reviewId, review) => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      warning('Please log in to vote on reviews', 5000);
      return;
    }

    // Check if user is trying to vote on their own review
    if (user && review.user_id === user.id) {
      warning('You cannot vote on your own review', 5000);
      return;
    }

    // Check if user has already voted
    if (userVotes[reviewId]) {
      info('You have already voted on this review', 3000);
      return;
    }

    try {
      // Set voting state to loading
      setVotingStates(prev => ({ ...prev, [reviewId]: 'loading' }));

      await reviewAPI.voteReview(reviewId, 'helpful');

      // Update the review's helpful count locally
      setReviews(prevReviews =>
        prevReviews.map(r =>
          r.id === reviewId
            ? { ...r, helpful_votes: (r.helpful_votes || 0) + 1 }
            : r
        )
      );

      // Update user votes state
      setUserVotes(prev => ({ ...prev, [reviewId]: 'helpful' }));

      // Set voting state to voted
      setVotingStates(prev => ({ ...prev, [reviewId]: 'voted' }));

      // Show success notification
      success('Thank you for your feedback!', 3000);

    } catch (error) {
      console.error('Failed to vote on review:', error);
      // Reset voting state on error
      setVotingStates(prev => ({ ...prev, [reviewId]: null }));

      // Show error message with better notifications
      if (error.message.includes('already voted')) {
        warning('You have already voted on this review', 5000);
      } else if (error.message.includes('own review')) {
        warning('You cannot vote on your own review', 5000);
      } else {
        error('Failed to vote on review. Please try again.', 5000);
      }
    }
  };

  // Handle page change
  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
      loadReviews(product.id, page);
    }
  };

  const handleReviewSubmit = async (reviewData) => {
    try {
      console.log('Review submitted:', reviewData);

      // Create the review
      const newReview = await reviewAPI.createReview(product.id, {
        rating: reviewData.rating,
        title: reviewData.title,
        comment: reviewData.comment,
        selected_color: selectedColor?.name,
        selected_size: selectedSize
      });

      // Upload images if any
      if (reviewData.images && reviewData.images.length > 0) {
        try {
          await reviewAPI.uploadReviewImages(newReview.id, reviewData.images);
        } catch (imageError) {
          console.error('Failed to upload review images:', imageError);
          // Continue even if image upload fails
        }
      }

      // Reload reviews to show the new one (go to first page to see new review)
      setCurrentPage(1);
      await loadReviews(product.id, 1);

      // Update user review status
      await checkUserReview(product.id);

      alert('Thank you for your review! It has been submitted successfully.');
    } catch (error) {
      console.error('Failed to submit review:', error);
      alert('Failed to submit review. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">

      <motion.div
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
        className="relative z-10 container mx-auto px-4 md:px-8 py-4 md:py-6"
      >
      {/* Breadcrumb */}
      <motion.div
        variants={fadeIn}
        className="mb-4 md:mb-6"
      >
        <div className="flex items-center text-sm text-gray-400">
          <Link to="/" className="hover:text-white">Home</Link>
          <ChevronRight size={16} className="mx-2" />
          <Link to="/collections" className="hover:text-white">Collections</Link>
          <ChevronRight size={16} className="mx-2" />
          {/* <Link
            to={`/category/${product.category.toLowerCase().replace(/\s+/g, '-')}`}
            className="hover:text-white"
          >
            {product.category}
          </Link> */}
          {/* <ChevronRight size={16} className="mx-2" /> */}
          <span className="text-gray-300">{product.name}</span>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 lg:gap-12">
        {/* Product Images */}
        <motion.div variants={fadeIn}>
          <div
            className="relative mb-4 rounded-lg overflow-hidden h-[400px] sm:h-[450px] md:h-[500px] lg:h-[550px] bg-white border border-[#2a2a2a] group transition-all duration-300"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <AnimatePresence mode="wait">
              <motion.img
                key={currentImage}
                src={(() => {
                  const image = getCurrentImages()[currentImage];
                  // Optimize Cloudinary URLs if they exist
                  return image && image.includes('cloudinary.com')
                    ? image.replace('/upload/', '/upload/w_400,h_400,c_fill,f_auto,q_auto/')
                    : image;
                })()}
                alt={product.name}
                className="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              />
            </AnimatePresence>



            {/* Sale Badge */}
            {product.is_sale === 1 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, delay: 0.2 }}
                className="absolute top-4 right-4 bg-gradient-to-br from-red-500 to-red-600 text-white text-sm font-bold w-16 h-16 rounded-full flex flex-col items-center justify-center z-30 shadow-lg border-2 border-red-400/30"
              >
                <span className="text-xs">SALE</span>
                <span className="text-base">{Math.round((1 - parseFloat(product.sale_price) / parseFloat(product.price)) * 100)}%</span>
              </motion.div>
            )}



            {/* Navigation Arrows - Desktop Only */}
            {getCurrentImages().length > 1 && (
              <>
                <button
                  className="absolute top-1/2 -translate-y-1/2 left-4 w-10 h-10 rounded-full bg-[#2a2a2a] flex items-center justify-center text-[#d4d4d4] hover:bg-[#404040] transition-colors backdrop-blur-sm z-30 hidden md:flex"
                  onClick={handlePrevImage}
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  className="absolute top-1/2 -translate-y-1/2 right-4 w-10 h-10 rounded-full bg-[#2a2a2a] flex items-center justify-center text-[#d4d4d4] hover:bg-[#404040] transition-colors backdrop-blur-sm z-30 hidden md:flex"
                  onClick={handleNextImage}
                >
                  <ChevronRight size={20} />
                </button>
              </>
            )}

            {/* Mobile Swipe Indicator */}
            {getCurrentImages().length > 1 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-[#2a2a2a] text-[#d4d4d4] text-xs px-3 py-1 rounded-full backdrop-blur-sm md:hidden">
                Swipe to navigate
              </div>
            )}
          </div>

          {/* Thumbnail Gallery */}
          <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
            {getCurrentImages().map((image, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`cursor-pointer rounded-md overflow-hidden w-20 h-20 flex-shrink-0 transition-all border ${
                  currentImage === index
                    ? 'ring-2 ring-[#f5f5f5] shadow-lg shadow-white/20 border-[#f5f5f5]'
                    : 'opacity-70 hover:opacity-100 border-[#2a2a2a]'
                }`}
                onClick={() => setCurrentImage(index)}
              >
                <img
                  src={image && image.includes('cloudinary.com')
                    ? image.replace('/upload/', '/upload/w_150,h_150,c_fill,f_auto,q_auto/')
                    : image}
                  alt={`${product.name} thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Product Details */}
        <motion.div variants={fadeIn} className="flex flex-col">
          {/* Product Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-['Montserrat',sans-serif] font-bold text-[#f5f5f5] mb-2">{product.name}</h1>
                <div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-2">
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <RatingStars rating={reviewStats.average_rating || 0} />
                    <span className="text-white text-sm sm:text-base">({reviewStats.total_reviews || 0})</span>
                  </div>
                  <span className="text-white hidden sm:inline">|</span>
                  <span className="text-[#22c55e] flex items-center gap-1 flex-shrink-0">
                    <Check size={14} className="text-[#22c55e]" />
                    <span className="text-sm sm:text-base font-medium">In Stock</span>
                  </span>
                </div>
                {/*<p className="text-white mb-4">SKU: {product.id}XYZ | {product.category}</p>*/}
              </div>
              <div className="flex gap-2">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleWishlistToggle}
                  className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-300 hover:bg-gray-700 transition-colors"
                >
                  <Heart
                    size={18}
                    className={isWishlisted ? 'fill-red-500 text-red-500' : ''}
                  />
                </motion.button>
                <motion.div className="relative share-menu-container">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setShowShareMenu(!showShareMenu)}
                    className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-300 hover:bg-gray-700 transition-colors"
                  >
                    <Share2 size={18} />
                  </motion.button>

                  <AnimatePresence>
                    {showShareMenu && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        className="absolute right-0 top-12 bg-gray-800 rounded-lg p-2 shadow-xl z-50"
                      >
                        <div className="flex flex-col gap-2">
                          {/* Social Media Share Buttons */}
                          <div className="flex gap-2">
                            <FacebookShareButton url={shareUrl}>
                              <FacebookIcon size={32} round />
                            </FacebookShareButton>
                            <TwitterShareButton url={shareUrl}>
                              <TwitterIcon size={32} round />
                            </TwitterShareButton>
                            <WhatsappShareButton url={shareUrl}>
                              <WhatsappIcon size={32} round />
                            </WhatsappShareButton>
                          </div>

                          {/* Copy Link Button */}
                          <div className="border-t border-gray-700 pt-2">
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={handleCopyLink}
                              disabled={copySuccess}
                              className={`w-full flex items-center justify-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 min-w-[120px] ${
                                copySuccess
                                  ? 'bg-green-600 text-white cursor-default'
                                  : 'bg-gray-700 hover:bg-gray-600 text-gray-200 hover:text-white'
                              }`}
                              title={copySuccess ? 'Link copied to clipboard!' : 'Copy product link to clipboard'}
                            >
                              <motion.div
                                animate={copySuccess ? { scale: [1, 1.2, 1] } : {}}
                                transition={{ duration: 0.3 }}
                              >
                                <Copy size={16} />
                              </motion.div>
                              {copySuccess ? 'Link Copied!' : 'Copy Link'}
                            </motion.button>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Price Display */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="my-3"
          >
            <div className="flex items-center gap-3 mb-2">
              {(() => {
                // Use the already transformed values from dataService
                const price = parseFloat(product.price) || 0;
                const salePrice = product.sale_price !== null ? parseFloat(product.sale_price) : null;
                
                // Log the values for debugging
                console.log('💰 Price Display:', {
                  price,
                  salePrice,
                  is_sale: product.is_sale,
                  hasValidSalePrice: salePrice !== null && salePrice > 0 && salePrice < price
                });
                
                // Show sale price if it's valid and less than regular price
                if (salePrice !== null && salePrice > 0 && salePrice < price) {
                  const discount = price - salePrice;
                  const discountPercent = Math.round((discount / price) * 100);
                  
                  return (
                    <>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="text-3xl font-bold text-white">₹{salePrice.toFixed(2)}</span>
                          <span className="text-gray-400 line-through text-xl">₹{price.toFixed(2)}</span>
                        </div>
                        <p className="text-red-400 text-sm">
                          Save ₹{discount.toFixed(2)} ({discountPercent}%)
                        </p>
                      </div>
                    </>
                  );
                }
                
                // Default to regular price
                return <span className="text-3xl font-bold text-white">₹{price.toFixed(2)}</span>;
              })()}
            </div>
          </motion.div>

          {/* Product description summary */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-gray-300 mb-4 leading-relaxed border-b border-gray-800 pb-4"
          >
            {product.description}
          </motion.p>

          {/* Color Selection */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-4"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-white font-medium">
                Color: <span className="text-gray-300">{selectedColor?.name}</span>
              </h3>
            </div>
            <motion.div
              className="flex gap-4"
              variants={staggerContainer}
              initial="hidden"
              animate="visible"
            >
              {product.colors.map((color) => (
                <motion.div
                  key={color.name}
                  variants={fadeIn}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  className={`relative cursor-pointer w-12 h-12 rounded-full border-2 transition-all ${
                    selectedColor?.name === color.name
                      ? 'border-white shadow-lg shadow-white/30'
                      : 'border-gray-500 hover:border-gray-300'
                  }`}
                  onClick={() => handleColorChange(color)}
                >
                  <div
                    className="absolute inset-1 rounded-full border border-gray-600"
                    style={{ backgroundColor: color.value }}
                  />
                  {selectedColor?.name === color.name && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute inset-0 flex items-center justify-center"
                    >
                      <div className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center">
                        <Check size={12} className="text-white" />
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Size Selection */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mb-4"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-white font-medium">
                Size: <span className="text-gray-300">{selectedSize}</span>
              </h3>
              <button
                className="text-gray-300 text-sm hover:text-white flex items-center gap-1"
                onClick={() => setShowSizeChart(true)}
              >
                <Info size={14} />
                Size Guide
              </button>
            </div>
            <div className="flex flex-wrap gap-3">
              {product.sizes.map((sizeObj) => {
                const sizeName = sizeObj.size_name || sizeObj;
                const isAvailable = sizeObj.is_available === 1 || sizeObj.is_available === true;
                const isSelected = selectedSize === sizeName;

                return (
                  <motion.button
                    key={sizeName}
                    whileHover={isAvailable ? { scale: 1.05 } : {}}
                    whileTap={isAvailable ? { scale: 0.95 } : {}}
                    disabled={!isAvailable}
                    className={`relative min-w-[50px] h-10 px-3 rounded-lg overflow-hidden transition-all duration-200 ${
                      !isAvailable
                        ? 'bg-gray-900 text-gray-600 cursor-not-allowed opacity-50'
                        : isSelected
                        ? 'bg-[#FF6F35] text-white'
                        : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    }`}
                    onClick={() => isAvailable && setSelectedSize(sizeName)}
                  >
                    {/* Selected size background */}
                    {isSelected && isAvailable && (
                      <motion.div
                        layoutId="selectedSize"
                        className="absolute inset-0 bg-gradient-to-r from-blue-500 to-teal-500 -z-10"
                      />
                    )}

                    {/* Size text */}
                    <span className={`relative z-10 ${!isAvailable ? 'line-through' : ''}`}>
                      {sizeName}
                    </span>

                    {/* Out of stock diagonal line */}
                    {!isAvailable && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-full h-0.5 bg-red-500 transform rotate-45 opacity-80"></div>
                      </div>
                    )}
                  </motion.button>
                );
              })}
            </div>
          </motion.div>

          {/* Purchase & Outfit Actions Section */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="space-y-4 mb-8"
          >
            {/* Primary Add to Cart Button */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <ProductCartButton 
                product={product}
                selectedColor={selectedColor}
                selectedSize={selectedSize}
              />
            </motion.div>

            {/* Elegant Divider */}
            <div className="relative my-3">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gradient-to-r from-transparent via-gray-600/30 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <div className="bg-[#0f172a] px-4 py-1.5 rounded-full border border-gray-700/30">
                  <span className="text-gray-400 text-xs sm:text-sm font-medium flex items-center gap-2">
                    {/* <Sparkles size={14} className="text-purple-400" /> */}
                    Style it with AI
                    {/* <Sparkles size={14} className="text-green-400" /> */}
                  </span>
                </div>
              </div>
            </div>

            {/* Outfit Building Section */}
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
              className="space-y-3"
            >
              {/* Section Header */}
              <div className="text-center mb-3">
                <h3 className="text-white font-semibold text-base sm:text-lg mb-1">Complete Your Look</h3>
                <p className="text-gray-400 text-xs sm:text-sm">
                  Let our AI help you create the perfect outfit with matching pieces
                </p>
              </div>

              {/* Feature Highlights - Single Row Responsive */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="flex justify-center items-center gap-1 sm:gap-2 md:gap-3 mb-3 px-2"
              >
                <div className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 bg-gray-900/30 rounded-full border border-gray-700/30 flex-1 max-w-[110px] sm:max-w-none sm:flex-initial">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Wand2 size={10} className="text-purple-400 sm:w-3 sm:h-3" />
                  </div>
                  <span className="text-white text-[10px] sm:text-xs font-medium truncate">
                    <span className="hidden sm:inline">AI Color Matching</span>
                    <span className="sm:hidden">AI Color Match</span>
                  </span>
                </div>

                <div className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 bg-gray-900/30 rounded-full border border-gray-700/30 flex-1 max-w-[110px] sm:max-w-none sm:flex-initial">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Shirt size={10} className="text-green-400 sm:w-3 sm:h-3" />
                  </div>
                  <span className="text-white text-[10px] sm:text-xs font-medium truncate">
                    <span className="hidden sm:inline">Style Compatibility</span>
                    <span className="sm:hidden">Style Match</span>
                  </span>
                </div>

                <div className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 bg-gray-900/30 rounded-full border border-gray-700/30 flex-1 max-w-[110px] sm:max-w-none sm:flex-initial">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Star size={10} className="text-blue-400 sm:w-3 sm:h-3" />
                  </div>
                  <span className="text-white text-[10px] sm:text-xs font-medium truncate">
                    <span className="hidden sm:inline">Curated Collections</span>
                    <span className="sm:hidden">Collections</span>
                  </span>
                </div>
              </motion.div>

              {/* Outfit Action Buttons */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Build Complete Outfit */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.9 }}
                  className="order-1"
                >
                  <QuickAddToOutfit
                    currentProduct={product}
                    selectedColor={selectedColor}
                  />
                </motion.div>

                {/* Add to Custom Outfit */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                  className="order-2"
                >
                  <CustomOutfitButton
                    product={product}
                    selectedColor={selectedColor}
                    selectedSize={selectedSize}
                  />
                </motion.div>
              </div>
            </motion.div>
          </motion.div>

          {/* Benefits */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
            className="grid grid-cols-2 gap-3 mb-4"
          >
            <div className="flex items-center gap-2 text-gray-300">
              <Truck size={16} className="text-gray-400" />
              <span className="text-sm">Free shipping over $50</span>
            </div>
            <div className="flex items-center gap-2 text-gray-300">
              <RefreshCw size={16} className="text-gray-400" />
              <span className="text-sm">30-day returns</span>
            </div>
            <div className="flex items-center gap-2 text-gray-300">
              <ShieldCheck size={16} className="text-gray-400" />
              <span className="text-sm">2-year warranty</span>
            </div>
            <div className="flex items-center gap-2 text-gray-300">
              <Tag size={16} className="text-gray-400" />
              <span className="text-sm">Secure checkout</span>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Product Details Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="mt-4 md:mt-6 mb-8"
      >
        <div className="border-b border-[#2a2a2a]">
          <div className="flex overflow-x-auto scrollbar-hide">
            <button
              onClick={() => setActiveTab('description')}
              className={`px-6 py-3 font-medium whitespace-nowrap transition-colors ${
                activeTab === 'description'
                  ? 'text-[#FF6F35] border-b-2 border-[#FF6F35]'
                  : 'text-[#AAAAAA] hover:text-gray-200'
              }`}
            >
              Description
            </button>
            <button
              onClick={() => setActiveTab('details')}
              className={`px-6 py-3 font-medium whitespace-nowrap transition-colors ${
                activeTab === 'details'
                  ? 'text-[#FF6F35] border-b-2 border-[#FF6F35]'
                  : 'text-[#AAAAAA] hover:text-gray-200'
              }`}
            >
              Product Details
            </button>
            <button
              onClick={() => setActiveTab('reviews')}
              className={`px-6 py-3 font-medium whitespace-nowrap transition-colors ${
                activeTab === 'reviews'
                  ? 'text-[#FF6F35] border-b-2 border-[#FF6F35]'
                  : 'text-[#AAAAAA] hover:text-gray-200'
              }`}
            >
              Reviews ({reviewStats.total_reviews || 0})
            </button>
            <button
              onClick={() => setActiveTab('shipping')}
              className={`px-6 py-3 font-medium whitespace-nowrap transition-colors ${
                activeTab === 'shipping'
                  ? 'text-[#FF6F35] border-b-2 border-[#FF6F35]'
                  : 'text-[#AAAAAA] hover:text-gray-200'
              }`}
            >
              Shipping & Returns
            </button>
          </div>
        </div>

        <div className="py-6">
          <AnimatePresence mode="wait">
            {activeTab === 'description' && (
              <motion.div
                key="description"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                className="text-[#AAAAAA] leading-relaxed max-w-3xl"
              >
                <p className="mb-4">
                  {product.description} Our design team worked tirelessly to create a product that combines style, comfort, and durability.
                  Each piece is crafted with attention to detail, using premium materials that ensure longevity while maintaining a sleek aesthetic.
                </p>
                <p className="mb-4">
                  The ergonomic design adapts to your body, providing superior comfort throughout the day. Whether you're dressing for a casual
                  outing or a formal event, this versatile piece transitions seamlessly between different settings.
                </p>
                <p>
                  We've implemented innovative technology to enhance performance, with moisture-wicking properties that keep you feeling fresh and
                  comfortable. The thoughtful design elements reflect our commitment to both style and functionality.
                </p>
              </motion.div>
            )}

            {activeTab === 'details' && (
              <motion.div
                key="details"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#404040]">
                    <h3 className="text-lg font-semibold text-white mb-3">Materials</h3>
                    <ul className="text-[#AAAAAA] space-y-2">
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Premium quality fabrics
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Eco-friendly production
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Hypoallergenic materials
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        UV protection treatment
                      </li>
                    </ul>
                  </div>
                  <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#404040]">
                    <h3 className="text-lg font-semibold text-white mb-3">Features</h3>
                    <ul className="text-[#AAAAAA] space-y-2">
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Moisture-wicking technology
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Reinforced stitching
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Hidden pockets
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Adjustable fit
                      </li>
                    </ul>
                  </div>
                  <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#404040]">
                    <h3 className="text-lg font-semibold text-white mb-3">Care Instructions</h3>
                    <ul className="text-[#AAAAAA] space-y-2">
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Machine washable
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Tumble dry low
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        No bleach
                      </li>
                      <li className="flex items-center gap-2">
                        <Check size={16} className="text-green-500" />
                        Iron on low heat if necessary
                      </li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'reviews' && (
              <motion.div
                key="reviews"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                {/* Reviews Summary */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                  {/* Rating Overview */}
                  <div className="lg:col-span-1 bg-[#1a1a1a] p-6 rounded-lg border border-[#404040]">
                    <div className="text-center mb-6">
                      <div className="text-5xl font-bold text-white mb-2">{reviewStats.average_rating?.toFixed(1) || '0.0'}</div>
                      <div className="flex justify-center mb-1">
                        <RatingStars rating={reviewStats.average_rating || 0} />
                      </div>
                      <p className="text-[#AAAAAA] text-sm">Based on {reviewStats.total_reviews || 0} reviews</p>
                    </div>

                    <div className="space-y-2">
                      {[5, 4, 3, 2, 1].map((stars) => {
                        const count = reviewStats.rating_distribution?.[stars] || 0;
                        const percentage = reviewStats.total_reviews > 0 ? Math.round((count / reviewStats.total_reviews) * 100) : 0;
                        return (
                          <div key={stars} className="flex items-center gap-2">
                            <div className="text-sm text-[#AAAAAA] w-6">{stars}</div>
                            <div className="w-6">
                              <Star size={14} className="fill-yellow-400 text-yellow-400" />
                            </div>
                            <div className="flex-1 h-3 bg-[#2a2a2a] rounded-full overflow-hidden">
                              <motion.div
                                className="h-full bg-[#FF6F35]"
                                initial={{ width: 0 }}
                                animate={{ width: `${percentage}%` }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                              />
                            </div>
                            <div className="text-sm text-[#AAAAAA] w-8">{percentage}%</div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Call to action */}
                  <div className="lg:col-span-2 bg-[#1a1a1a] p-6 rounded-lg border border-[#404040] flex flex-col justify-center items-center text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Share your experience</h3>
                    <p className="text-[#AAAAAA] mb-6">
                      {userHasReviewed
                        ? "Thank you for your review! You have already shared your experience with this product."
                        : "Your feedback helps other shoppers make informed decisions and helps us improve our products."
                      }
                    </p>
                    {userReviewLoading ? (
                      <div className="flex items-center gap-2 text-[#AAAAAA]">
                        <div className="w-4 h-4 border-2 border-[#AAAAAA]/30 border-t-[#AAAAAA] rounded-full animate-spin" />
                        Checking review status...
                      </div>
                    ) : userHasReviewed ? (
                      <div className="px-6 py-3 bg-[#2a2a2a] text-[#AAAAAA] rounded-lg font-medium flex items-center gap-2 min-h-[44px] cursor-not-allowed">
                        <Check size={18} />
                        Review Already Submitted
                      </div>
                    ) : (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-3 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 flex items-center gap-2 min-h-[44px]"
                        style={{
                          background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)'
                        }}
                        onClick={() => setShowReviewModal(true)}
                      >
                        <MessageCircle size={18} />
                        Write a Review
                      </motion.button>
                    )}
                  </div>
                </div>

                {/* Review Form */}
                <AnimatePresence>
                  {showReviewForm && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mb-12 overflow-hidden"
                    >
                      <div className="bg-gray-900 p-6 rounded-lg">
                        <h3 className="text-xl font-semibold text-white mb-6">Write Your Review</h3>
                        <div className="space-y-6">
                          <div>
                            <label className="block text-gray-300 mb-2">Your Rating*</label>
                            <div className="flex gap-2">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <motion.button
                                  key={star}
                                  whileHover={{ scale: 1.2 }}
                                  whileTap={{ scale: 0.9 }}
                                  className="text-gray-400 hover:text-yellow-400  "
                                >
                                  <Star size={24} />
                                </motion.button>
                              ))}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <label className="block text-gray-300 mb-2">Review Title*</label>
                              <input
                                type="text"
                                className="w-full bg-gray-800 text-white border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                placeholder="Summarize your experience"
                              />
                            </div>
                            <div>
                              <label className="block text-gray-300 mb-2">Your Name*</label>
                              <input
                                type="text"
                                className="w-full bg-gray-800 text-white border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                placeholder="Your name"
                              />
                            </div>
                          </div>

                          <div>
                            <label className="block text-gray-300 mb-2">Your Review*</label>
                            <textarea
                              className="w-full bg-gray-800 text-white border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 min-h-[120px]"
                              placeholder="Share your thoughts about the product"
                            ></textarea>
                          </div>

                          <div>
                            <label className="block text-gray-300 mb-2">Upload Photos (optional)</label>
                            <div className="border-2 border-dashed border-gray-700 rounded-lg p-6 text-center">
                              <div className="flex flex-col items-center">
                                <div className="w-12 h-12 rounded-full bg-gray-800 flex items-center justify-center mb-3">
                                  <Plus size={24} className="text-gray-400" />
                                </div>
                                <p className="text-gray-400 mb-3">Drag and drop images or click to browse</p>
                                <button className="text-indigo-400 underline text-sm">Browse Files</button>
                              </div>
                            </div>
                          </div>

                          <div className="flex justify-end">
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              className="px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-500 transition-colors"
                            >
                              Submit Review
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Reviews List */}
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-semibold text-white">Customer Reviews</h3>
                      {totalPages > 1 && (
                        <p className="text-[#AAAAAA] text-sm mt-1">
                          Page {currentPage} of {totalPages} • Showing {reviewsPerPage} reviews per page
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-[#AAAAAA] text-sm">Sort by:</span>
                      <select className="bg-[#2a2a2a] text-white border border-[#404040] rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option>Most Recent</option>
                        <option>Highest Rated</option>
                        <option>Lowest Rated</option>
                        <option>Most Helpful</option>
                      </select>
                    </div>
                  </div>

                  {reviewsLoading ? (
                    <div className="flex justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF6B35]"></div>
                    </div>
                  ) : reviews.length > 0 ? (
                    <motion.div
                      className="space-y-6"
                      variants={staggerContainer}
                      initial="hidden"
                      animate="visible"
                    >
                      {reviews.map((review) => (
                      <motion.div
                        key={review.id}
                        variants={fadeIn}
                        className="bg-[#1a1a1a] p-6 rounded-lg border border-[#404040]"
                      >
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-[#2a2a2a] flex items-center justify-center text-[#AAAAAA] font-medium">
                              {review.user?.initial || review.user?.name?.charAt(0)?.toUpperCase() || 'U'}
                            </div>
                            <div>
                              <h4 className="text-white font-medium flex items-center gap-2">
                                {review.user?.name || 'Anonymous'}
                                {review.is_verified_purchase && (
                                  <span className="bg-green-900/40 text-green-400 text-xs px-2 py-0.5 rounded-full flex items-center gap-1">
                                    <Check size={10} />
                                    Verified Purchase
                                  </span>
                                )}
                              </h4>
                              <div className="flex items-center gap-2 text-sm text-[#AAAAAA]">
                                <RatingStars rating={review.rating} />
                                <span>·</span>
                                <span>{new Date(review.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <h3 className="text-lg font-medium text-white mb-2">{review.title}</h3>
                        <p className="text-[#AAAAAA] mb-4">{review.comment}</p>

                        {review.images && review.images.length > 0 && (
                          <div className="flex gap-3 mb-4">
                            {review.images.map((image, index) => (
                              <div
                                key={index}
                                className="w-20 h-20 rounded-md overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                                onClick={() => {
                                  setLightboxImages(review.images);
                                  setLightboxIndex(index);
                                  setLightboxReviewTitle(review.title);
                                  setLightboxReviewerName(review.username);
                                  setLightboxOpen(true);
                                }}
                              >
                                <img
                                  src={image.image_url}
                                  alt={image.alt_text || `Review ${index + 1}`}
                                  className="w-full h-full object-cover hover:scale-105 transition-transform"
                                />
                              </div>
                            ))}
                          </div>
                        )}

                        <div className="flex items-center justify-between pt-3 border-t border-[#2a2a2a]">
                          <div className="flex items-center gap-2 text-sm">
                            {(() => {
                              const isOwnReview = user && review.user_id === user.id;
                              const hasVoted = userVotes[review.id];
                              const isVoting = votingStates[review.id] === 'loading';
                              const justVoted = votingStates[review.id] === 'voted';

                              if (isOwnReview) {
                                return (
                                  <div className="flex items-center gap-1 text-[#666] cursor-not-allowed">
                                    <ThumbsUp size={16} />
                                    <span>Your Review ({review.helpful_votes || 0})</span>
                                  </div>
                                );
                              }

                              if (!isAuthenticated) {
                                return (
                                  <button
                                    onClick={() => handleHelpfulVote(review.id, review)}
                                    className="flex items-center gap-1 text-[#AAAAAA] hover:text-white transition-colors"
                                  >
                                    <ThumbsUp size={16} />
                                    <span>Helpful ({review.helpful_votes || 0})</span>
                                  </button>
                                );
                              }

                              return (
                                <button
                                  onClick={() => handleHelpfulVote(review.id, review)}
                                  disabled={isVoting || hasVoted || justVoted}
                                  className={`flex items-center gap-1 transition-colors ${
                                    hasVoted || justVoted
                                      ? 'text-green-400 cursor-not-allowed'
                                      : isVoting
                                      ? 'text-[#AAAAAA] cursor-not-allowed'
                                      : 'text-[#AAAAAA] hover:text-white'
                                  }`}
                                >
                                  <motion.div
                                    whileHover={hasVoted || justVoted || isVoting ? {} : { scale: 1.2 }}
                                    whileTap={hasVoted || justVoted || isVoting ? {} : { scale: 0.9 }}
                                  >
                                    {isVoting ? (
                                      <div className="w-4 h-4 border-2 border-[#AAAAAA]/30 border-t-[#AAAAAA] rounded-full animate-spin" />
                                    ) : (
                                      <ThumbsUp size={16} className={hasVoted || justVoted ? 'fill-current' : ''} />
                                    )}
                                  </motion.div>
                                  <span>
                                    {hasVoted || justVoted ? 'Voted' : 'Helpful'} ({review.helpful_votes || 0})
                                  </span>
                                </button>
                              );
                            })()}
                          </div>
                          <button className="text-indigo-400 text-sm hover:text-indigo-300">Report</button>
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#2a2a2a] flex items-center justify-center">
                        <Star size={24} className="text-[#AAAAAA]" />
                      </div>
                      <h3 className="text-xl font-semibold text-white mb-2">No reviews yet</h3>
                      <p className="text-[#AAAAAA] mb-6">Be the first to share your experience with this product!</p>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowReviewModal(true)}
                        className="bg-gradient-to-r from-[#FF6B35] to-[#F7931E] text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                      >
                        Write First Review
                      </motion.button>
                    </div>
                  )}

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center mt-8">
                      <div className="flex">
                        {/* Previous button */}
                        <button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className={`w-10 h-10 flex items-center justify-center rounded-l-lg transition-colors ${
                            currentPage === 1
                              ? 'bg-[#1a1a1a] text-[#666] cursor-not-allowed'
                              : 'bg-[#2a2a2a] text-[#AAAAAA] hover:bg-[#404040]'
                          }`}
                        >
                          <ChevronLeft size={18} />
                        </button>

                        {/* Page numbers */}
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => {
                          // Show first page, last page, current page, and pages around current page
                          const showPage = page === 1 || page === totalPages ||
                                         (page >= currentPage - 1 && page <= currentPage + 1);

                          if (!showPage) {
                            // Show ellipsis for gaps
                            if (page === currentPage - 2 || page === currentPage + 2) {
                              return (
                                <span key={page} className="w-10 h-10 flex items-center justify-center text-[#AAAAAA]">
                                  ...
                                </span>
                              );
                            }
                            return null;
                          }

                          return (
                            <button
                              key={page}
                              onClick={() => handlePageChange(page)}
                              className={`w-10 h-10 flex items-center justify-center transition-colors ${
                                currentPage === page
                                  ? 'bg-[#FF6B35] text-white'
                                  : 'bg-[#2a2a2a] text-[#AAAAAA] hover:bg-[#404040]'
                              }`}
                            >
                              {page}
                            </button>
                          );
                        })}

                        {/* Next button */}
                        <button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className={`w-10 h-10 flex items-center justify-center rounded-r-lg transition-colors ${
                            currentPage === totalPages
                              ? 'bg-[#1a1a1a] text-[#666] cursor-not-allowed'
                              : 'bg-[#2a2a2a] text-[#AAAAAA] hover:bg-[#404040]'
                          }`}
                        >
                          <ChevronRight size={18} />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            {activeTab === 'shipping' && (
              <motion.div
                key="shipping"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#404040]">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                    <Truck size={18} className="text-indigo-400" />
                    Shipping Information
                  </h3>
                  <div className="space-y-4 text-[#AAAAAA]">
                    <p>
                      We ship to over 100 countries worldwide through trusted carriers. Delivery times vary based
                      on your location and shipping method selected during checkout.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border border-[#404040] rounded-lg p-4 bg-[#2a2a2a]">
                        <h4 className="font-medium text-white mb-2">Standard Shipping</h4>
                        <ul className="space-y-1 text-sm">
                          <li>Delivery in 5-7 business days</li>
                          <li>Free for orders over $50</li>
                          <li>$4.99 for orders under $50</li>
                        </ul>
                      </div>
                      <div className="border border-[#404040] rounded-lg p-4 bg-[#2a2a2a]">
                        <h4 className="font-medium text-white mb-2">Express Shipping</h4>
                        <ul className="space-y-1 text-sm">
                          <li>Delivery in 1-3 business days</li>
                          <li>$12.99 for all orders</li>
                          <li>Free for orders over $150</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#404040]">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                    <RefreshCw size={18} className="text-indigo-400" />
                    Returns & Exchanges
                  </h3>
                  <div className="space-y-4 text-[#AAAAAA]">
                    <p>
                      We want you to be completely satisfied with your purchase. If you're not happy for any reason,
                      we accept returns and exchanges within 30 days of delivery.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border border-[#404040] rounded-lg p-4 bg-[#2a2a2a]">
                        <h4 className="font-medium text-white mb-2">Return Policy</h4>
                        <ul className="space-y-1 text-sm">
                          <li>30-day return window</li>
                          <li>Items must be unworn with original tags</li>
                          <li>Free returns within the US</li>
                        </ul>
                      </div>
                      <div className="border border-[#404040] rounded-lg p-4 bg-[#2a2a2a]">
                        <h4 className="font-medium text-white mb-2">Exchange Process</h4>
                        <ul className="space-y-1 text-sm">
                          <li>Request an exchange through your account</li>
                          <li>Print a prepaid shipping label</li>
                          <li>Drop off at any postal location</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>



      {/* Similar Products */}
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
      >
        <SimilarProducts currentProductId={product.id} category={product.category} />
      </motion.div>

      {/* Size Chart Modal */}
      <AnimatePresence>
        {showSizeChart && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[#1a1a1a] border border-[#404040] rounded-xl overflow-hidden max-w-3xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-white">Size Guide</h3>
                  <button
                    onClick={() => setShowSizeChart(false)}
                    className="w-8 h-8 rounded-full bg-[#2a2a2a] flex items-center justify-center text-[#AAAAAA] hover:bg-[#404040]"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>

                <SizeChart />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Review Modal */}
      <ReviewModal
        isOpen={showReviewModal}
        onClose={() => setShowReviewModal(false)}
        productName={product.name}
        onSubmit={handleReviewSubmit}
      />
      
      {/* Wishlist Notification */}
      <AnimatedNotification
        show={showWishlistNotification}
        type="wishlist"
        isAdded={wishlistAction === 'added'}
      />

      {/* Image Lightbox */}
      <ImageLightbox
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        images={lightboxImages}
        initialIndex={lightboxIndex}
        reviewTitle={lightboxReviewTitle}
        reviewerName={lightboxReviewerName}
      />
    </motion.div>
    </div>
  );
}