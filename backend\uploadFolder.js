// upload-folder.js
import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Cloudinary configuration
cloudinary.config({
  cloud_name: 'dsp0zmfcx',
  api_key: '336461674786964',
  api_secret: 'qAUvO-stA_YrE6f3e1hBmw4hpYc'
});

const folderPath = path.join(__dirname, 'ProductsImages');
const cloudFolder = 'ProductsImages';

// Supported file extensions
const supportedImageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'];
const supportedVideoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm'];
const supportedExtensions = [...supportedImageExtensions, ...supportedVideoExtensions];

// Function to check if file is supported
const isSupportedFile = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  return supportedExtensions.includes(ext);
};

// Function to check if file is an image
const isImageFile = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  return supportedImageExtensions.includes(ext);
};

// Function to check if file is a video
const isVideoFile = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  return supportedVideoExtensions.includes(ext);
};

// Function to get file size in MB
const getFileSizeMB = async (filePath) => {
  const stats = await fs.stat(filePath);
  return stats.size / (1024 * 1024);
};

// Function to recursively scan folders for supported files
const scanFolderRecursively = async (folderPath, basePath = folderPath) => {
  const allFiles = [];

  const scanFolder = async (currentPath, relativePath = '') => {
    const items = await fs.readdir(currentPath);

    for (const item of items) {
      const itemPath = path.join(currentPath, item);
      const stats = await fs.stat(itemPath);

      if (stats.isDirectory()) {
        // Recursively scan subdirectories
        const newRelativePath = relativePath ? path.join(relativePath, item) : item;
        await scanFolder(itemPath, newRelativePath);
      } else if (stats.isFile() && isSupportedFile(item)) {
        const fileRelativePath = relativePath ? path.join(relativePath, item) : item;
        allFiles.push({
          filename: item,
          fullPath: itemPath,
          relativePath: fileRelativePath,
          isImage: isImageFile(item),
          isVideo: isVideoFile(item)
        });
      }
    }
  };

  await scanFolder(folderPath);
  return allFiles;
};

// Function to compress image if it's too large
const compressImageIfNeeded = async (filePath, filename, maxSizeMB = 9.5) => {
  const sizeMB = await getFileSizeMB(filePath);

  if (sizeMB <= maxSizeMB) {
    return filePath; // No compression needed
  }

  console.log(`🔧 Compressing ${filename} (${sizeMB.toFixed(1)}MB -> target: ${maxSizeMB}MB)`);

  const ext = path.extname(filename).toLowerCase();
  const compressedDir = path.join(path.dirname(filePath), 'compressed');

  // Create compressed directory if it doesn't exist
  try {
    await fs.mkdir(compressedDir, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }

  const compressedPath = path.join(compressedDir, filename);

  try {
    let quality = 85; // Start with 85% quality
    let compressedSizeMB = sizeMB;

    // Try different quality levels until we get under the limit
    while (quality >= 20 && compressedSizeMB > maxSizeMB) {
      if (ext === '.png') {
        await sharp(filePath)
          .png({ quality, compressionLevel: 9 })
          .toFile(compressedPath);
      } else {
        await sharp(filePath)
          .jpeg({ quality, progressive: true })
          .toFile(compressedPath);
      }

      compressedSizeMB = await getFileSizeMB(compressedPath);

      if (compressedSizeMB > maxSizeMB) {
        quality -= 10; // Reduce quality and try again
      }
    }

    const finalSizeMB = await getFileSizeMB(compressedPath);
    console.log(`✅ Compressed ${filename}: ${sizeMB.toFixed(1)}MB -> ${finalSizeMB.toFixed(1)}MB (${quality}% quality)`);

    return compressedPath;
  } catch (error) {
    console.log(`⚠️  Compression failed for ${filename}, using original: ${error.message}`);
    return filePath;
  }
};

// Function to upload a single file with retry mechanism
const uploadFileWithRetry = async (fileInfo, maxRetries = 3) => {
  const { fullPath, relativePath, filename, isImage, isVideo } = fileInfo;

  // Only compress images, not videos
  const processedFilePath = isImage ? await compressImageIfNeeded(fullPath, filename) : fullPath;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Use the relative path to maintain folder structure in Cloudinary
      const cloudinaryPath = path.join(cloudFolder, relativePath).replace(/\\/g, '/');
      const publicId = cloudinaryPath.replace(path.extname(cloudinaryPath), '');

      const result = await cloudinary.uploader.upload(processedFilePath, {
        public_id: publicId,
        use_filename: false,
        unique_filename: false,
        overwrite: true,
        resource_type: isVideo ? 'video' : 'image'
      });

      return {
        success: true,
        filename,
        relativePath,
        url: result.secure_url,
        public_id: result.public_id,
        compressed: isImage && processedFilePath !== fullPath,
        isVideo
      };
    } catch (error) {
      if (attempt === maxRetries) {
        return {
          success: false,
          filename,
          error: error.message
        };
      }
      console.log(`⚠️  Retry ${attempt}/${maxRetries} for ${filename}...`);
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};

// Main upload function
const uploadAllImages = async () => {
  try {
    console.log('🚀 Starting Cloudinary upload process...');
    console.log(`📁 Source folder: ${folderPath}`);
    console.log(`☁️  Cloudinary folder: ${cloudFolder}`);
    console.log('─'.repeat(60));

    // Check if folder exists
    try {
      await fs.access(folderPath);
    } catch (error) {
      throw new Error(`ProductsImages folder not found at: ${folderPath}`);
    }

    // Scan folder recursively for all supported files
    const allFiles = await scanFolderRecursively(folderPath);

    if (allFiles.length === 0) {
      console.log('❌ No supported files found in the ProductsImages folder');
      return;
    }

    const imageFiles = allFiles.filter(f => f.isImage);
    const videoFiles = allFiles.filter(f => f.isVideo);

    console.log(`📁 Found ${allFiles.length} files to upload:`);
    if (imageFiles.length > 0) {
      console.log(`📸 Images (${imageFiles.length}):`);
      imageFiles.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.relativePath}`);
      });
    }
    if (videoFiles.length > 0) {
      console.log(`🎬 Videos (${videoFiles.length}):`);
      videoFiles.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.relativePath}`);
      });
    }
    console.log('─'.repeat(60));

    // Upload all files concurrently with progress tracking
    const uploadPromises = allFiles.map(async (fileInfo, index) => {
      console.log(`⏳ [${index + 1}/${allFiles.length}] Uploading: ${fileInfo.relativePath} ${fileInfo.isVideo ? '🎬' : '📸'}`);

      const result = await uploadFileWithRetry(fileInfo);

      if (result.success) {
        const typeIcon = result.isVideo ? '🎬' : '📸';
        const extraInfo = result.compressed ? ' (compressed)' : '';
        console.log(`✅ [${index + 1}/${allFiles.length}] Success: ${fileInfo.relativePath} ${typeIcon}${extraInfo}`);
        console.log(`   📎 URL: ${result.url}`);
      } else {
        console.log(`❌ [${index + 1}/${allFiles.length}] Failed: ${fileInfo.relativePath}`);
        console.log(`   💥 Error: ${result.error}`);
      }

      return result;
    });

    // Wait for all uploads to complete
    console.log('⏳ Processing uploads...');
    const results = await Promise.all(uploadPromises);

    // Summary
    console.log('─'.repeat(60));
    console.log('📊 UPLOAD SUMMARY:');

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    console.log(`✅ Successful uploads: ${successful.length}`);
    console.log(`❌ Failed uploads: ${failed.length}`);
    console.log(`📈 Success rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);

    if (successful.length > 0) {
      console.log('\n🎉 Successfully uploaded files:');
      successful.forEach((result, index) => {
        const typeIcon = result.isVideo ? '🎬' : '📸';
        console.log(`   ${index + 1}. ${result.relativePath} ${typeIcon} -> ${result.url}`);
      });
    }

    if (failed.length > 0) {
      console.log('\n💥 Failed uploads:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.relativePath} - ${result.error}`);
      });
    }

    console.log('─'.repeat(60));
    console.log(failed.length === 0 ? '🎊 All uploads completed successfully!' : '⚠️  Some uploads failed. Check errors above.');

  } catch (error) {
    console.error('💥 Fatal error during upload process:', error.message);
    process.exit(1);
  }
};

// Run the upload process
uploadAllImages();
