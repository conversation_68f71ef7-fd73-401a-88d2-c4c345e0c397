import { createContext, useContext, useState, useCallback } from 'react';

// Create notification context
const NotificationContext = createContext();

// Types of notifications
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// Notification Provider Component
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  // Add a notification
  const addNotification = useCallback((message, type = NOTIFICATION_TYPES.INFO, duration = 5000) => {
    const id = Date.now() + Math.random().toString(36).substring(2, 7);

    setNotifications(prev => [
      ...prev,
      { id, message, type, duration }
    ]);

    // Auto-remove notification after duration
    if (duration !== 0) {
      setTimeout(() => {
        removeNotification(id);
      }, duration);
    }

    return id;
  }, []);

  // Remove a notification by id
  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  // Shorthand methods for different notification types
  const success = useCallback((message, duration) =>
    addNotification(message, NOTIFICATION_TYPES.SUCCESS, duration), [addNotification]);

  const error = useCallback((message, duration) =>
    addNotification(message, NOTIFICATION_TYPES.ERROR, duration), [addNotification]);

  const warning = useCallback((message, duration) =>
    addNotification(message, NOTIFICATION_TYPES.WARNING, duration), [addNotification]);

  const info = useCallback((message, duration) =>
    addNotification(message, NOTIFICATION_TYPES.INFO, duration), [addNotification]);

  // Clear all notifications
  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const value = {
    notifications,
    addNotification,
    removeNotification,
    success,
    error,
    warning,
    info,
    clearAll
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook to use notification context
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    // Return safe fallback instead of throwing error
    return {
      notifications: [],
      addNotification: (message, type) => console.log('Notification:', message, type),
      removeNotification: () => {},
      success: (message) => console.log('Success:', message),
      error: (message) => console.log('Error:', message),
      warning: (message) => console.log('Warning:', message),
      info: (message) => console.log('Info:', message),
      clearAll: () => {}
    };
  }
  return context;
};