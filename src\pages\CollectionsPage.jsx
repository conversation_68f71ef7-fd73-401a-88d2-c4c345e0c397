import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const collections = [
  // All Oversized Items Together
  {
    title: 'Oversized Tees',
    description: 'Ultra-loose fit tees with unique designs and premium comfort',
    image: 'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_700,h_700,c_fill/v1752099103/ProductsImages/oversized5%281%29.png',
    link: '/category/oversized-tees',
    accent: 'from-violet-600 to-indigo-600'
  },
  // {
  //   title: 'Oversized T-Shirt',
  //   description: 'Relaxed oversized t-shirts for ultimate comfort and style',
  //   image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?q=80&w=700&auto=format&fit=crop',
  //   link: '/category/oversized-t-shirt',
  //   accent: 'from-orange-600 to-yellow-600'
  // },
  {
    title: 'Oversized Shirt',
    description: 'Loose-fitting button shirts with contemporary design',
    link: '/category/oversized-shirt',
    accent: 'from-purple-600 to-pink-600'
  },

  // All Shirts Together
  {
    title: 'Shirts',
    description: 'Premium shirts for both casual and semi-formal occasions',
    image: 'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_700,h_700,c_fill/v1752099099/ProductsImages/shirt1BL%281%29.png',
    link: '/category/shirts',
    accent: 'from-blue-600 to-indigo-600'
  },
  {
    title: 'Full Sleeves Shirt',
    description: 'Classic full sleeve shirts for formal and casual wear',
    link: '/category/full-sleeves-shirt',
    accent: 'from-green-600 to-teal-600'
  },
  {
    title: 'Shacket',
    description: 'Versatile shirt-jacket hybrid for layered looks',
    link: '/category/shacket',
    accent: 'from-amber-600 to-orange-600'
  },

  // All T-Shirts Together
  {
    title: 'T-Shirts',
    description: 'Classic fit essentials for everyday style',
    image: 'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_700,h_700,c_fill/v1752099694/ProductsImages/tshirt1%283%29.jpg',
    link: '/category/t-shirts',
    accent: 'from-cyan-600 to-blue-600'
  },
  {
    title: 'Regular Fit T-Shirt',
    description: 'Perfect fit t-shirts for everyday comfort',
    link: '/category/regular-fit-t-shirt',
    accent: 'from-teal-600 to-cyan-600'
  },

  // All Jeans Together
  {
    title: 'Baggy Jeans',
    description: 'Relaxed fit jeans with vintage streetwear appeal',
    link: '/category/baggy-jeans',
    accent: 'from-cyan-600 to-blue-600'
  },
  {
    title: 'Fit Jeans',
    description: 'Modern fit jeans with stretch comfort and style',
    link: '/category/fit-jeans',
    accent: 'from-rose-600 to-pink-600'
  },

  // Other Categories
  {
    title: 'Hoodies',
    description: 'Cozy and stylish hoodies for the perfect layered look',
    link: '/category/hoodies',
    accent: 'from-emerald-600 to-green-600'
  },
  {
    title: 'Sweatshirt',
    description: 'Comfortable sweatshirts for casual everyday wear',
    link: '/category/sweatshirt',
    accent: 'from-gray-700 to-gray-800'
  },
  {
    title: 'Jackets',
    description: 'Premium jackets for layering and style',
    link: '/category/jackets',
    accent: 'from-indigo-600 to-purple-600'
  },
  {
    title: 'Capri',
    description: 'Comfortable capri pants perfect for summer style',
    link: '/category/capri',
    accent: 'from-pink-600 to-rose-600'
  }
];

export default function CollectionsPage() {
  return (
    <div className="min-h-screen bg-black pt-8 sm:pt-12 pb-12 sm:pb-16">
      <div className="container mx-auto px-3 sm:px-4 md:px-8">
        {/* Header - Mobile Optimized */}
        <div className="text-center mb-6 sm:mb-8 md:mb-10">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-3xl sm:text-4xl md:text-5xl font-['Bebas_Neue',sans-serif] text-white mb-3 sm:mb-4"
          >
            OUR COLLECTIONS
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-gray-400 max-w-2xl mx-auto text-sm sm:text-base px-2 sm:px-0"
          >
            Explore our curated categories, each featuring premium quality apparel designed for style and comfort
          </motion.p>
        </div>

        {/* Collections Grid - Mobile Optimized */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4 md:gap-5 lg:gap-6 xl:gap-8">
          {collections.map((collection, index) => (
            <Link
              key={collection.title}
              to={collection.link}
              className="block"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="group relative overflow-hidden rounded-xl sm:rounded-2xl bg-[#0a0a0a] border border-[#2a2a2a] cursor-pointer hover:border-[#404040] transition-all duration-300"
              >
                {/* Background Image - Mobile Optimized Heights */}
                <div className="relative h-48 sm:h-64 md:h-80 lg:h-[400px] overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/90 z-10" />
                  <img
                    src={collection.image && collection.image.includes('cloudinary.com')
                      ? collection.image.replace('/upload/', '/upload/w_700,h_700,c_fill,f_auto,q_auto/')
                      : collection.image}
                    alt={collection.title}
                    className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"
                  />
                </div>

                {/* Content - Mobile Optimized Padding */}
                <div className="absolute inset-0 flex flex-col justify-end p-3 sm:p-4 md:p-6 lg:p-8 z-20">
                  <div className="relative">
                    <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-['Bebas_Neue',sans-serif] text-white leading-tight group-hover:text-[#FF6B35] transition-colors duration-300">
                      {collection.title}
                    </h2>
                  </div>
                </div>
              </motion.div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
